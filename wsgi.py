#!/usr/bin/env python3
"""
WSGI entry point for HavenHuts application
Production-ready configuration for CapRover deployment
"""

import os
import sys
import logging
from app import app

# Configure logging for production
if not app.debug:
    # Set up file logging
    if not os.path.exists('logs'):
        os.mkdir('logs')
    
    file_handler = logging.FileHandler('logs/havenhuts.log')
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    
    app.logger.setLevel(logging.INFO)
    app.logger.info('HavenHuts startup')

# Production configuration
app.config.update(
    # Security settings
    SESSION_COOKIE_SECURE=True,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    
    # Performance settings
    SEND_FILE_MAX_AGE_DEFAULT=31536000,  # 1 year for static files
    
    # Error handling
    PROPAGATE_EXCEPTIONS=True
)

# Add health check endpoint for CapRover
@app.route('/api/health')
def health_check():
    """Health check endpoint for load balancer"""
    try:
        # Basic health checks
        from pymongo import MongoClient
        
        # Check database connection
        client = MongoClient(os.getenv('MONGO_URI', 'mongodb://localhost:27017/'))
        client.admin.command('ping')
        
        return {
            'status': 'healthy',
            'service': 'HavenHuts',
            'version': '1.0.0',
            'database': 'connected'
        }, 200
    except Exception as e:
        app.logger.error(f'Health check failed: {e}')
        return {
            'status': 'unhealthy',
            'service': 'HavenHuts',
            'error': str(e)
        }, 503

if __name__ == "__main__":
    # This won't be used in production (gunicorn will import the app)
    # But useful for local testing
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
