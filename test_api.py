#!/usr/bin/env python3
"""
HavenHuts API Testing Script
Tests all the main API endpoints to ensure they're working correctly.
"""

import requests
import json
import base64
import os
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
ADMIN_USER = "admin"
ADMIN_PASS = "admin123"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['status']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_get_properties():
    """Test getting properties"""
    print("\n🔍 Testing GET /api/properties...")
    try:
        response = requests.get(f"{BASE_URL}/api/properties")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Properties retrieved: {len(data.get('data', []))} properties")
                print(f"   Total: {data.get('total', 0)}")
                return True
            else:
                print(f"❌ Properties API returned error: {data.get('error')}")
                return False
        else:
            print(f"❌ Properties API failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Properties API error: {e}")
        return False

def test_get_properties_with_filters():
    """Test getting properties with filters"""
    print("\n🔍 Testing GET /api/properties with filters...")
    try:
        params = {
            'city': 'San Francisco',
            'minPrice': 1000,
            'maxPrice': 2000,
            'page': 1,
            'per_page': 5
        }
        response = requests.get(f"{BASE_URL}/api/properties", params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Filtered properties retrieved: {len(data.get('data', []))} properties")
                return True
            else:
                print(f"❌ Filtered properties API returned error: {data.get('error')}")
                return False
        else:
            print(f"❌ Filtered properties API failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Filtered properties API error: {e}")
        return False

def test_vote_api():
    """Test the voting API"""
    print("\n🔍 Testing POST /api/vote...")
    try:
        # First get a property ID
        response = requests.get(f"{BASE_URL}/api/properties")
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                property_id = data['data'][0]['_id']
                
                # Test voting
                vote_data = {
                    'property_id': property_id,
                    'vote_type': 'up'
                }
                vote_response = requests.post(
                    f"{BASE_URL}/api/vote",
                    json=vote_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if vote_response.status_code == 200:
                    vote_result = vote_response.json()
                    if vote_result.get('success'):
                        print(f"✅ Vote recorded successfully")
                        print(f"   Upvotes: {vote_result.get('upvotes')}")
                        print(f"   Downvotes: {vote_result.get('downvotes')}")
                        return True
                    else:
                        print(f"❌ Vote API returned error: {vote_result.get('error')}")
                        return False
                else:
                    print(f"❌ Vote API failed: {vote_response.status_code}")
                    return False
            else:
                print("❌ No properties found to test voting")
                return False
        else:
            print("❌ Could not get properties for voting test")
            return False
    except Exception as e:
        print(f"❌ Vote API error: {e}")
        return False

def test_report_api():
    """Test the reporting API"""
    print("\n🔍 Testing POST /api/report...")
    try:
        # First get a property ID
        response = requests.get(f"{BASE_URL}/api/properties")
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                property_id = data['data'][0]['_id']
                
                # Test reporting
                report_data = {
                    'property_id': property_id,
                    'reason': 'Test report from API testing script'
                }
                report_response = requests.post(
                    f"{BASE_URL}/api/report",
                    json=report_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if report_response.status_code == 200:
                    report_result = report_response.json()
                    if report_result.get('success'):
                        print(f"✅ Report submitted successfully")
                        return True
                    else:
                        print(f"❌ Report API returned error: {report_result.get('error')}")
                        return False
                else:
                    print(f"❌ Report API failed: {report_response.status_code}")
                    return False
            else:
                print("❌ No properties found to test reporting")
                return False
        else:
            print("❌ Could not get properties for reporting test")
            return False
    except Exception as e:
        print(f"❌ Report API error: {e}")
        return False

def test_admin_dashboard():
    """Test the admin dashboard API"""
    print("\n🔍 Testing GET /admin (dashboard)...")
    try:
        auth_string = base64.b64encode(f"{ADMIN_USER}:{ADMIN_PASS}".encode()).decode()
        headers = {'Authorization': f'Basic {auth_string}'}
        
        response = requests.get(f"{BASE_URL}/admin", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('stats', {})
                print(f"✅ Admin dashboard accessed successfully")
                print(f"   Pending: {stats.get('pending', 0)}")
                print(f"   Reported: {stats.get('reported', 0)}")
                print(f"   Approved: {stats.get('approved', 0)}")
                print(f"   Total: {stats.get('total', 0)}")
                return True
            else:
                print(f"❌ Admin dashboard returned error: {data.get('error')}")
                return False
        elif response.status_code == 401:
            print("❌ Admin authentication failed - check ADMIN_USER and ADMIN_PASS")
            return False
        else:
            print(f"❌ Admin dashboard failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Admin dashboard error: {e}")
        return False

def test_frontend_pages():
    """Test that frontend pages are accessible"""
    print("\n🔍 Testing frontend pages...")
    try:
        # Test index page
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ Index page accessible")
        else:
            print(f"❌ Index page failed: {response.status_code}")
            return False
        
        # Test post property page
        response = requests.get(f"{BASE_URL}/post-property")
        if response.status_code == 200:
            print("✅ Post property page accessible")
        else:
            print(f"❌ Post property page failed: {response.status_code}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Frontend pages error: {e}")
        return False

def main():
    """Run all API tests"""
    print("🧪 HavenHuts API Testing Suite")
    print("=" * 50)
    print(f"Testing server at: {BASE_URL}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print("=" * 50)
    
    tests = [
        test_health_check,
        test_frontend_pages,
        test_get_properties,
        test_get_properties_with_filters,
        test_vote_api,
        test_report_api,
        test_admin_dashboard,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your HavenHuts backend is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        print("\nCommon issues:")
        print("- Make sure the server is running on the correct port")
        print("- Check your MongoDB connection")
        print("- Verify environment variables are set correctly")
        print("- Ensure admin credentials are correct")
    
    print("=" * 50)

if __name__ == '__main__':
    main()
