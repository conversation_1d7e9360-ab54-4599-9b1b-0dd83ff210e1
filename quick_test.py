#!/usr/bin/env python3
"""
Quick test script to check if HavenHuts backend is working
"""

import os
import sys

def check_environment():
    """Check if environment is properly configured"""
    print("🔍 Checking environment configuration...")

    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please copy .env.example to .env and configure your settings.")
        return False

    # Check critical environment variables
    from dotenv import load_dotenv
    load_dotenv()

    required_vars = {
        'MONGO_URI': 'MongoDB connection string',
        'CLOUDINARY_CLOUD_NAME': 'Cloudinary cloud name',
        'CLOUDINARY_API_KEY': 'Cloudinary API key',
        'CLOUDINARY_API_SECRET': 'Cloudinary API secret'
    }

    missing_vars = []
    for var, description in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        print("❌ Missing environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        return False

    print("✅ Environment configuration looks good!")
    return True

def test_imports():
    """Test if all required packages are installed"""
    print("\n🔍 Testing package imports...")

    try:
        import flask
        print(f"✅ Flask {flask.__version__}")
    except ImportError:
        print("❌ Flask not installed")
        return False

    try:
        import pymongo
        print(f"✅ PyMongo {pymongo.__version__}")
    except ImportError:
        print("❌ PyMongo not installed")
        return False

    try:
        import cloudinary
        print("✅ Cloudinary")
    except ImportError:
        print("❌ Cloudinary not installed")
        return False

    try:
        from flask_cors import CORS
        print("✅ Flask-CORS")
    except ImportError:
        print("❌ Flask-CORS not installed")
        return False

    try:
        from flask_limiter import Limiter
        print("✅ Flask-Limiter")
    except ImportError:
        print("❌ Flask-Limiter not installed")
        return False

    return True

def test_mongodb_connection():
    """Test MongoDB connection"""
    print("\n🔍 Testing MongoDB connection...")

    try:
        from dotenv import load_dotenv
        load_dotenv()

        from pymongo import MongoClient

        mongo_uri = os.getenv('MONGO_URI')
        client = MongoClient(mongo_uri)

        # Test connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful!")

        # Test database access
        db_name = os.getenv('MONGO_DB_NAME', 'havenhuts')
        db = client[db_name]

        # Try to access properties collection
        properties_collection = db.properties
        count = properties_collection.count_documents({})
        print(f"✅ Found {count} properties in database")

        client.close()
        return True

    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        return False

def test_cloudinary_connection():
    """Test Cloudinary connection"""
    print("\n🔍 Testing Cloudinary connection...")

    try:
        from dotenv import load_dotenv
        load_dotenv()

        import cloudinary

        cloudinary.config(
            cloud_name=os.getenv('CLOUDINARY_CLOUD_NAME'),
            api_key=os.getenv('CLOUDINARY_API_KEY'),
            api_secret=os.getenv('CLOUDINARY_API_SECRET'),
            secure=True
        )

        # Test connection by trying to access the API
        try:
            import cloudinary.api
            result = cloudinary.api.ping()
            if result.get('status') == 'ok':
                print("✅ Cloudinary connection successful!")
                return True
            else:
                print("❌ Cloudinary connection failed")
                return False
        except Exception as api_error:
            print(f"❌ Cloudinary API test failed: {api_error}")
            print("✅ Cloudinary basic import works (config may need checking)")
            return True  # Basic import works, just API test failed

    except Exception as e:
        print(f"❌ Cloudinary connection failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 HavenHuts Backend Quick Test")
    print("=" * 40)

    tests = [
        ("Environment Configuration", check_environment),
        ("Package Imports", test_imports),
        ("MongoDB Connection", test_mongodb_connection),
        ("Cloudinary Connection", test_cloudinary_connection),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n💡 Fix the {test_name.lower()} issues above and try again.")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")

    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Your backend should work correctly.")
        print("\nNext steps:")
        print("1. Run: python run.py")
        print("2. Open: http://localhost:5000")
        print("3. Test the API with: python test_api.py")
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
        print("\nCommon solutions:")
        print("- Install missing packages: pip install -r requirements.txt")
        print("- Configure .env file with your credentials")
        print("- Check MongoDB and Cloudinary credentials")

    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
