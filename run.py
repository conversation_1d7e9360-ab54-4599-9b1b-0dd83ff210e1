#!/usr/bin/env python3
"""
HavenHuts Backend Startup Script
"""

import os
import sys
from app import app, logger

def main():
    """Main function to start the HavenHuts backend server"""
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        logger.warning("No .env file found. Please copy .env.example to .env and configure your settings.")
        print("\n⚠️  Warning: No .env file found!")
        print("Please run: cp .env.example .env")
        print("Then edit .env with your configuration settings.\n")
        
        # Ask if user wants to continue anyway
        response = input("Continue with default settings? (y/N): ").lower()
        if response != 'y':
            sys.exit(1)
    
    # Get configuration
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_ENV') == 'development'
    host = os.getenv('HOST', '0.0.0.0')
    
    # Print startup information
    print("🏠 HavenHuts Backend Server")
    print("=" * 40)
    print(f"Environment: {os.getenv('FLASK_ENV', 'production')}")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"Debug: {debug}")
    print(f"MongoDB: {os.getenv('MONGO_URI', 'Not configured')}")
    print(f"Cloudinary: {'Configured' if os.getenv('CLOUDINARY_CLOUD_NAME') else 'Not configured'}")
    print("=" * 40)
    
    # Check critical environment variables
    missing_vars = []
    critical_vars = ['MONGO_URI', 'CLOUDINARY_CLOUD_NAME', 'CLOUDINARY_API_KEY', 'CLOUDINARY_API_SECRET']
    
    for var in critical_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n❌ Missing critical environment variables: {', '.join(missing_vars)}")
        print("Please configure these in your .env file before starting the server.")
        sys.exit(1)
    
    print("\n🚀 Starting server...")
    print(f"📱 Frontend: http://localhost:{port}")
    print(f"🔧 Admin Panel: http://localhost:{port}/admin/panel")
    print(f"📊 Health Check: http://localhost:{port}/health")
    print(f"📚 API Docs: See README.md for API endpoints")
    print("\nPress Ctrl+C to stop the server\n")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        print(f"\n❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
