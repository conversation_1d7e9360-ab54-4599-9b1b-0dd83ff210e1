#!/usr/bin/env python3
"""
Test script to verify admin panel authentication
"""

import requests
import base64

def test_admin_authentication():
    """Test admin panel with correct credentials"""
    print("🧪 Testing Admin Panel Authentication...")
    
    # Test with credentials from .env file
    admin_user = "admin"
    admin_pass = "Interpreter@1435"
    
    try:
        # Test admin dashboard
        response = requests.get(
            'http://localhost:5000/admin',
            auth=(admin_user, admin_pass),
            timeout=10
        )
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('stats', {})
                print("✅ Admin authentication successful!")
                print(f"   Pending: {stats.get('pending', 0)}")
                print(f"   Approved: {stats.get('approved', 0)}")
                print(f"   Reported: {stats.get('reported', 0)}")
                print(f"   Total: {stats.get('total', 0)}")
                return True
            else:
                print(f"❌ Admin dashboard error: {result.get('error')}")
                return False
        elif response.status_code == 401:
            print("❌ Authentication failed - Invalid credentials")
            print(f"   Tried: {admin_user}:{admin_pass}")
            return False
        else:
            print(f"❌ HTTP Error {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_admin_panel_page():
    """Test admin panel HTML page"""
    print("\n🧪 Testing Admin Panel HTML Page...")
    
    try:
        response = requests.get(
            'http://localhost:5000/admin/panel',
            auth=('admin', 'Interpreter@1435'),
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Admin panel page accessible")
            print(f"   Content length: {len(response.text)} characters")
            return True
        elif response.status_code == 401:
            print("❌ Admin panel page authentication failed")
            return False
        else:
            print(f"❌ Admin panel page error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_pending_properties():
    """Test getting pending properties"""
    print("\n🧪 Testing Pending Properties Endpoint...")
    
    try:
        response = requests.get(
            'http://localhost:5000/admin/properties/pending',
            auth=('admin', 'Interpreter@1435'),
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                properties = result.get('data', [])
                print(f"✅ Found {len(properties)} pending properties")
                for prop in properties:
                    print(f"   - {prop.get('title', 'Unknown')} (ID: {prop.get('_id', 'Unknown')})")
                return True
            else:
                print(f"❌ Pending properties error: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def main():
    """Run all admin tests"""
    print("🧪 HavenHuts Admin Panel Test")
    print("=" * 50)
    
    # Test admin authentication
    auth_success = test_admin_authentication()
    
    # Test admin panel page
    page_success = test_admin_panel_page()
    
    # Test pending properties
    pending_success = test_pending_properties()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Admin Authentication: {'✅ PASSED' if auth_success else '❌ FAILED'}")
    print(f"   Admin Panel Page: {'✅ PASSED' if page_success else '❌ FAILED'}")
    print(f"   Pending Properties: {'✅ PASSED' if pending_success else '❌ FAILED'}")
    
    if auth_success and page_success:
        print("\n🎉 Admin panel is working!")
        print("   You can access it at: http://localhost:5000/admin/panel")
        print("   Credentials: admin / Interpreter@1435")
    else:
        print("\n⚠️  Admin panel has issues.")
        print("   Check the server logs and environment variables.")
    
    return auth_success and page_success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
