#!/usr/bin/env python3
"""
Test to verify HavenHuts is ready for CapRover deployment
"""

import os
import json
import subprocess
import sys

def check_file_exists(filepath, description):
    """Check if a required file exists"""
    if os.path.exists(filepath):
        print(f"   ✅ {description}: {filepath}")
        return True
    else:
        print(f"   ❌ {description}: {filepath} (MISSING)")
        return False

def check_dockerfile():
    """Check Dockerfile configuration"""
    print("🐳 Checking Dockerfile...")

    if not os.path.exists('Dockerfile'):
        print("   ❌ Dockerfile missing")
        return False

    with open('Dockerfile', 'r', encoding='utf-8') as f:
        content = f.read()

    checks = [
        ('python:3.11', 'Python 3.11 base image'),
        ('gunicorn', 'Gunicorn server'),
        ('EXPOSE 5000', 'Port 5000 exposed'),
        ('wsgi:app', 'WSGI entry point'),
        ('HEALTHCHECK', 'Health check configured')
    ]

    all_good = True
    for check, description in checks:
        if check in content:
            print(f"   ✅ {description}")
        else:
            print(f"   ❌ {description} (MISSING)")
            all_good = False

    return all_good

def check_requirements():
    """Check requirements.txt"""
    print("\n📦 Checking requirements.txt...")

    if not os.path.exists('requirements.txt'):
        print("   ❌ requirements.txt missing")
        return False

    with open('requirements.txt', 'r', encoding='utf-8') as f:
        content = f.read()

    required_packages = [
        'flask',
        'gunicorn',
        'pymongo',
        'cloudinary',
        'flask-cors',
        'flask-limiter',
        'pillow'
    ]

    all_good = True
    for package in required_packages:
        if package.lower() in content.lower():
            print(f"   ✅ {package}")
        else:
            print(f"   ❌ {package} (MISSING)")
            all_good = False

    return all_good

def check_github_workflow():
    """Check GitHub Actions workflow"""
    print("\n🔄 Checking GitHub Actions workflow...")

    workflow_path = '.github/workflows/deploy.yml'
    if not os.path.exists(workflow_path):
        print("   ❌ GitHub Actions workflow missing")
        return False

    with open(workflow_path, 'r', encoding='utf-8') as f:
        content = f.read()

    checks = [
        ('caprover/deploy-from-github', 'CapRover deployment action'),
        ('CAPROVER_SERVER', 'CapRover server secret'),
        ('APP_NAME', 'App name secret'),
        ('APP_TOKEN', 'App token secret'),
        ('deploy.tar', 'Deployment bundle')
    ]

    all_good = True
    for check, description in checks:
        if check in content:
            print(f"   ✅ {description}")
        else:
            print(f"   ❌ {description} (MISSING)")
            all_good = False

    return all_good

def check_environment_template():
    """Check environment template"""
    print("\n🔧 Checking environment configuration...")

    if not os.path.exists('.env.example'):
        print("   ❌ .env.example missing")
        return False

    with open('.env.example', 'r', encoding='utf-8') as f:
        content = f.read()

    required_vars = [
        'SECRET_KEY',
        'MONGO_URI',
        'CLOUDINARY_CLOUD_NAME',
        'CLOUDINARY_API_KEY',
        'CLOUDINARY_API_SECRET',
        'ADMIN_USER',
        'ADMIN_PASS'
    ]

    all_good = True
    for var in required_vars:
        if var in content:
            print(f"   ✅ {var}")
        else:
            print(f"   ❌ {var} (MISSING)")
            all_good = False

    return all_good

def check_app_configuration():
    """Check app.py for production readiness"""
    print("\n⚙️ Checking app configuration...")

    if not os.path.exists('app.py'):
        print("   ❌ app.py missing")
        return False

    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()

    checks = [
        ('FLASK_ENV', 'Production environment detection'),
        ('gunicorn', 'Gunicorn compatibility'),
        ('health', 'Health check endpoint'),
        ('Talisman', 'Security headers'),
        ('DDoSProtection', 'DDoS protection'),
        ('rate_limiting', 'Rate limiting')
    ]

    all_good = True
    for check, description in checks:
        if check.lower() in content.lower():
            print(f"   ✅ {description}")
        else:
            print(f"   ⚠️  {description} (check manually)")

    return True  # Don't fail on app checks

def main():
    """Run all deployment readiness checks"""
    print("🚀 HavenHuts CapRover Deployment Readiness Check")
    print("=" * 60)

    # Check required files
    print("📁 Checking required files...")
    files_check = all([
        check_file_exists('app.py', 'Main application'),
        check_file_exists('wsgi.py', 'WSGI entry point'),
        check_file_exists('requirements.txt', 'Python dependencies'),
        check_file_exists('Dockerfile', 'Docker configuration'),
        check_file_exists('captain-definition', 'CapRover definition'),
        check_file_exists('.env.example', 'Environment template'),
        check_file_exists('.github/workflows/deploy.yml', 'GitHub Actions workflow'),
        check_file_exists('templates/', 'Templates directory'),
        check_file_exists('static/', 'Static files directory')
    ])

    # Run detailed checks
    dockerfile_check = check_dockerfile()
    requirements_check = check_requirements()
    workflow_check = check_github_workflow()
    env_check = check_environment_template()
    app_check = check_app_configuration()

    print("\n" + "=" * 60)
    print("📊 DEPLOYMENT READINESS SUMMARY:")
    print(f"   Required Files: {'✅ READY' if files_check else '❌ MISSING FILES'}")
    print(f"   Dockerfile: {'✅ READY' if dockerfile_check else '❌ NEEDS WORK'}")
    print(f"   Requirements: {'✅ READY' if requirements_check else '❌ NEEDS WORK'}")
    print(f"   GitHub Actions: {'✅ READY' if workflow_check else '❌ NEEDS WORK'}")
    print(f"   Environment: {'✅ READY' if env_check else '❌ NEEDS WORK'}")
    print(f"   App Config: {'✅ READY' if app_check else '❌ NEEDS WORK'}")

    all_ready = all([files_check, dockerfile_check, requirements_check, workflow_check, env_check, app_check])

    if all_ready:
        print("\n🎉 DEPLOYMENT READY!")
        print("\n✅ Your HavenHuts platform is ready for CapRover deployment!")
        print("\n📋 Next Steps:")
        print("   1. Set up CapRover server and create app")
        print("   2. Configure GitHub repository secrets:")
        print("      • CAPROVER_SERVER")
        print("      • APP_NAME")
        print("      • APP_TOKEN")
        print("   3. Set up MongoDB Atlas database")
        print("   4. Configure Cloudinary account")
        print("   5. Push to main branch to trigger deployment")

        print("\n📖 See DEPLOYMENT.md for detailed instructions")

        return True
    else:
        print("\n⚠️  DEPLOYMENT NOT READY")
        print("Please fix the issues above before deploying.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
