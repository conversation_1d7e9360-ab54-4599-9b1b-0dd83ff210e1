#!/usr/bin/env python3
"""
Complete system test for HavenHuts
Tests all major functionality including the fixes
"""

import requests
import time

def test_homepage():
    """Test homepage loads without JavaScript errors"""
    print("🧪 Testing Homepage...")
    
    try:
        response = requests.get('http://localhost:5000/', timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for the fixed JavaScript variables
            if 'let properties = [];' in content and 'let filteredProperties = [];' in content:
                print("✅ Homepage JavaScript variables properly declared")
            else:
                print("❌ JavaScript variables not found or incorrect")
                return False
            
            # Check for essential elements
            if 'propertiesGrid' in content and 'loadingState' in content:
                print("✅ Homepage essential elements present")
            else:
                print("❌ Essential homepage elements missing")
                return False
            
            print("✅ Homepage loads successfully")
            return True
        else:
            print(f"❌ Homepage failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Homepage test failed: {e}")
        return False

def test_admin_login_page():
    """Test admin login page loads"""
    print("\n🧪 Testing Admin Login Page...")
    
    try:
        response = requests.get('http://localhost:5000/admin/login', timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for login form elements
            if 'loginForm' in content and 'username' in content and 'password' in content:
                print("✅ Admin login form elements present")
            else:
                print("❌ Admin login form elements missing")
                return False
            
            print("✅ Admin login page loads successfully")
            return True
        else:
            print(f"❌ Admin login page failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Admin login page test failed: {e}")
        return False

def test_admin_panel_redirect():
    """Test admin panel redirects to login when not authenticated"""
    print("\n🧪 Testing Admin Panel Redirect...")
    
    try:
        response = requests.get('http://localhost:5000/admin/panel', timeout=10, allow_redirects=False)
        
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            if '/admin/login' in location:
                print("✅ Admin panel correctly redirects to login")
                return True
            else:
                print(f"❌ Admin panel redirects to wrong location: {location}")
                return False
        else:
            print(f"❌ Admin panel should redirect but returned: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Admin panel redirect test failed: {e}")
        return False

def test_api_properties():
    """Test properties API endpoint"""
    print("\n🧪 Testing Properties API...")
    
    try:
        response = requests.get('http://localhost:5000/api/properties', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                properties = data.get('data', [])
                print(f"✅ Properties API working - found {len(properties)} properties")
                return True
            else:
                print(f"❌ Properties API returned error: {data.get('error')}")
                return False
        else:
            print(f"❌ Properties API failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Properties API test failed: {e}")
        return False

def test_admin_authentication():
    """Test admin authentication with correct credentials"""
    print("\n🧪 Testing Admin Authentication...")
    
    try:
        response = requests.get(
            'http://localhost:5000/admin',
            auth=('admin', 'Interpreter@1435'),
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('stats', {})
                print("✅ Admin authentication successful")
                print(f"   Stats: {stats}")
                return True
            else:
                print(f"❌ Admin authentication failed: {data.get('error')}")
                return False
        else:
            print(f"❌ Admin authentication failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Admin authentication test failed: {e}")
        return False

def test_property_creation():
    """Test property creation (without images)"""
    print("\n🧪 Testing Property Creation...")
    
    property_data = {
        'title': 'System Test Property',
        'description': 'This is a comprehensive test property created by the automated system test to verify that property creation is working correctly without any issues.',
        'address': '456 Test Avenue',
        'city': 'Test City',
        'price': '1500',
        'bedrooms': '3',
        'bathrooms': '2',
        'furnished_status': 'semi',
        'phone': '+1234567890'
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/api/properties',
            data=property_data,
            timeout=10
        )
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print("✅ Property creation successful")
                print(f"   Property ID: {result.get('property_id')}")
                return True, result.get('property_id')
            else:
                print(f"❌ Property creation failed: {result.get('error')}")
                return False, None
        else:
            print(f"❌ Property creation failed: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Property creation test failed: {e}")
        return False, None

def main():
    """Run all system tests"""
    print("🧪 HavenHuts Complete System Test")
    print("=" * 60)
    
    tests = [
        ("Homepage", test_homepage),
        ("Admin Login Page", test_admin_login_page),
        ("Admin Panel Redirect", test_admin_panel_redirect),
        ("Properties API", test_api_properties),
        ("Admin Authentication", test_admin_authentication),
        ("Property Creation", lambda: test_property_creation()[0]),
    ]
    
    passed = 0
    total = len(tests)
    failed_tests = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
            else:
                failed_tests.append(test_name)
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            failed_tests.append(test_name)
    
    print("\n" + "=" * 60)
    print(f"📊 System Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Your HavenHuts system is fully functional!")
        print("\n✅ What's Working:")
        print("   - Homepage loads without JavaScript errors")
        print("   - Admin login page is accessible")
        print("   - Admin panel properly redirects unauthenticated users")
        print("   - Properties API is working")
        print("   - Admin authentication is working")
        print("   - Property creation is working (without images)")
        
        print("\n🚀 Next Steps:")
        print("   1. Go to http://localhost:5000/admin/login")
        print("   2. Login with: admin / Interpreter@1435")
        print("   3. Approve pending properties")
        print("   4. Check homepage to see approved properties")
        print("   5. Test the full user workflow")
        
    else:
        print(f"⚠️  {len(failed_tests)} tests failed:")
        for test in failed_tests:
            print(f"   - {test}")
        
        print("\n💡 Issues to fix:")
        if "Homepage" in failed_tests:
            print("   - Check browser console for JavaScript errors")
        if "Admin Login Page" in failed_tests:
            print("   - Check admin login template")
        if "Admin Panel Redirect" in failed_tests:
            print("   - Check admin panel authentication logic")
        if "Properties API" in failed_tests:
            print("   - Check database connection and API endpoints")
        if "Admin Authentication" in failed_tests:
            print("   - Check admin credentials in .env file")
        if "Property Creation" in failed_tests:
            print("   - Check property creation API and validation")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
