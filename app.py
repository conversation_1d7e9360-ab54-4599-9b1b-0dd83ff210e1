import os
import logging
import io
from datetime import datetime, timezone
from functools import wraps
from bson import ObjectId
from bson.errors import InvalidId
import cloudinary
import cloudinary.uploader
from flask import Flask, request, jsonify, render_template, abort, redirect, session
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
# from flask_talisman import Talisman  # Commented out for development
import hashlib
import time
from collections import defaultdict, deque
from pymongo import MongoClient, ASCENDING, TEXT
from dotenv import load_dotenv
import re
from PIL import Image, ImageOps

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize CORS
CORS(app, origins=os.getenv('ALLOWED_ORIGINS', '*').split(','))

# Security Configuration
app.config.update(
    SESSION_COOKIE_SECURE=False,  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=3600,  # 1 hour
)

# Initialize Talisman for security headers
csp = {
    'default-src': "'self'",
    'script-src': "'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://kit.fontawesome.com",
    'style-src': "'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com",
    'font-src': "'self' https://fonts.gstatic.com https://ka-f.fontawesome.com",
    'img-src': "'self' data: https: http:",
    'connect-src': "'self' https:",
    'frame-ancestors': "'none'",
    'base-uri': "'self'",
    'form-action': "'self'"
}

# talisman = Talisman(
#     app,
#     force_https=False,  # Set to True in production
#     strict_transport_security=False,  # Set to True in production
#     content_security_policy=csp,
#     referrer_policy='strict-origin-when-cross-origin',
#     feature_policy={
#         'geolocation': "'none'",
#         'camera': "'none'",
#         'microphone': "'none'"
#     }
# )

# Advanced DDoS Protection
class DDoSProtection:
    def __init__(self):
        self.request_counts = defaultdict(deque)
        self.blocked_ips = defaultdict(float)
        self.suspicious_patterns = defaultdict(int)

    def is_blocked(self, ip):
        """Check if IP is currently blocked"""
        if ip in self.blocked_ips:
            if time.time() < self.blocked_ips[ip]:
                return True
            else:
                del self.blocked_ips[ip]
        return False

    def check_request(self, ip, endpoint):
        """Check if request should be allowed"""
        current_time = time.time()

        # Clean old requests (older than 1 minute)
        while self.request_counts[ip] and self.request_counts[ip][0] < current_time - 60:
            self.request_counts[ip].popleft()

        # Add current request
        self.request_counts[ip].append(current_time)

        # Check for DDoS patterns
        requests_per_minute = len(self.request_counts[ip])

        # Aggressive rate limiting for Kolkata startup
        if requests_per_minute > 60:  # More than 60 requests per minute
            self.block_ip(ip, 1800)  # Block for 30 minutes
            return False
        elif requests_per_minute > 30:  # More than 30 requests per minute
            self.block_ip(ip, 300)   # Block for 5 minutes
            return False
        elif requests_per_minute > 20:  # More than 20 requests per minute
            self.suspicious_patterns[ip] += 1
            if self.suspicious_patterns[ip] > 2:
                self.block_ip(ip, 600)  # Block for 10 minutes
                return False

        return True

    def block_ip(self, ip, duration):
        """Block IP for specified duration"""
        self.blocked_ips[ip] = time.time() + duration
        logger.warning(f"🚨 SECURITY: Blocked IP {ip} for {duration} seconds due to suspicious activity")

ddos_protection = DDoSProtection()

# Initialize rate limiter with enhanced limits
limiter = Limiter(
    key_func=get_remote_address,
    app=app,
    default_limits=["500 per day", "100 per hour", "20 per minute"],
    headers_enabled=True
)

# Configure Cloudinary
cloudinary.config(
    cloud_name=os.getenv('CLOUDINARY_CLOUD_NAME'),
    api_key=os.getenv('CLOUDINARY_API_KEY'),
    api_secret=os.getenv('CLOUDINARY_API_SECRET'),
    secure=True
)

# MongoDB connection
try:
    client = MongoClient(os.getenv('MONGO_URI', 'mongodb://localhost:27017/'))
    db = client[os.getenv('MONGO_DB_NAME', 'eznest')]
    properties_collection = db.properties

    # Create indexes for better performance
    properties_collection.create_index([("title", TEXT), ("description", TEXT)])
    properties_collection.create_index("city")
    properties_collection.create_index("price")
    properties_collection.create_index("bedrooms")
    properties_collection.create_index("furnished_status")
    properties_collection.create_index("status")
    properties_collection.create_index("created_at")

    logger.info("MongoDB connected and indexes created successfully")
except Exception as e:
    logger.error(f"MongoDB connection failed: {e}")
    db = None

# Admin authentication decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check session authentication first
        if session.get('admin_authenticated'):
            return f(*args, **kwargs)

        # Fallback to HTTP Basic Auth for API calls
        auth = request.authorization
        admin_user = os.getenv('ADMIN_USER', 'admin')
        admin_pass = os.getenv('ADMIN_PASS', 'admin123')

        if not auth or auth.username != admin_user or auth.password != admin_pass:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function

# Security middleware
@app.before_request
def security_checks():
    """Perform security checks before each request"""
    client_ip = get_remote_address()

    # Skip security checks for static files and localhost during development
    if request.endpoint == 'static' or client_ip in ['127.0.0.1', 'localhost']:
        return

    # DDoS Protection (disabled for localhost during development)
    # if ddos_protection.is_blocked(client_ip):
    #     logger.warning(f"🚨 SECURITY: Blocked request from {client_ip}")
    #     abort(429)  # Too Many Requests

    # if not ddos_protection.check_request(client_ip, request.endpoint):
    #     abort(429)

    # Block common attack patterns
    user_agent = request.headers.get('User-Agent', '').lower()
    suspicious_agents = ['sqlmap', 'nikto', 'nmap', 'masscan', 'zap', 'bot', 'crawler']
    if any(agent in user_agent for agent in suspicious_agents):
        logger.warning(f"🚨 SECURITY: Blocked suspicious user agent from {client_ip}: {user_agent}")
        abort(403)

    # Check for SQL injection patterns in query parameters
    for param_value in request.args.values():
        if any(pattern in param_value.lower() for pattern in ['union select', 'drop table', 'insert into', '1=1', 'or 1=1', 'script>', '<iframe']):
            logger.warning(f"🚨 SECURITY: Blocked injection attempt from {client_ip}")
            abort(403)

    # Validate Content-Type for POST requests
    if request.method == 'POST' and request.endpoint not in ['static', 'admin_login']:
        content_type = request.headers.get('Content-Type', '')
        if not any(ct in content_type for ct in ['application/json', 'multipart/form-data', 'application/x-www-form-urlencoded']):
            abort(400)

# Input validation and sanitization
def sanitize_input(text):
    """Sanitize user input to prevent XSS and injection attacks"""
    if not text:
        return text

    # Remove potentially dangerous characters
    dangerous_chars = ['<script', '</script', 'javascript:', 'data:', 'vbscript:', 'onload=', 'onerror=']
    text_lower = text.lower()
    for char in dangerous_chars:
        if char in text_lower:
            logger.warning(f"🚨 SECURITY: Blocked dangerous input: {text[:50]}...")
            return ""

    return text.strip()

def validate_indian_phone(phone):
    """Validate Indian phone number format"""
    # Remove all non-digit characters except +
    clean_phone = re.sub(r'[^\d\+]', '', phone)

    # Check for valid Indian phone number patterns
    patterns = [
        r'^\+91[6-9]\d{9}$',  # +91 followed by 10 digits starting with 6-9
        r'^[6-9]\d{9}$',      # 10 digits starting with 6-9
        r'^0[6-9]\d{9}$'      # 0 followed by 10 digits starting with 6-9
    ]

    return any(re.match(pattern, clean_phone) for pattern in patterns)

# Utility functions
def check_admin_auth(username, password):
    """Check admin authentication credentials"""
    admin_user = os.getenv('ADMIN_USER', 'admin')
    admin_pass = os.getenv('ADMIN_PASS', 'admin123')
    return username == admin_user and password == admin_pass

def validate_property_data(data):
    """Validate property form data - simplified for Kolkata startup"""
    # Only validate required fields: title, city, price, phone
    required_fields = ['title', 'city', 'price', 'phone']

    for field in required_fields:
        if not data.get(field):
            return False, f"Missing required field: {field}"

        # Sanitize input
        data[field] = sanitize_input(str(data[field]))

    try:
        price = float(data['price'])
        if price <= 0:
            return False, "Price must be a positive number"
    except (ValueError, TypeError):
        return False, "Invalid price value"

    # Validate Indian phone number
    if not validate_indian_phone(data['phone']):
        return False, "Please enter a valid Indian phone number (e.g., +91 98765 43210)"

    # Validate title length
    if len(data['title']) < 10:
        return False, "Property title must be at least 10 characters long"

    return True, None

def generate_slug(title):
    """Generate SEO-friendly slug from title"""
    slug = re.sub(r'[^a-zA-Z0-9\s-]', '', title.lower())
    slug = re.sub(r'\s+', '-', slug)
    slug = re.sub(r'-+', '-', slug)
    return slug.strip('-')

def compress_image(file, max_width=1200, max_height=900, quality=85):
    """Compress and optimize image for web display"""
    try:
        # Reset file pointer
        file.seek(0)

        # Open image with PIL
        image = Image.open(file)

        # Convert RGBA to RGB if necessary (for JPEG compatibility)
        if image.mode in ('RGBA', 'LA', 'P'):
            # Create a white background
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')

        # Auto-rotate based on EXIF data
        image = ImageOps.exif_transpose(image)

        # Calculate new dimensions while maintaining aspect ratio
        width, height = image.size
        if width > max_width or height > max_height:
            # Calculate scaling factor
            scale_w = max_width / width
            scale_h = max_height / height
            scale = min(scale_w, scale_h)

            new_width = int(width * scale)
            new_height = int(height * scale)

            # Resize image with high-quality resampling
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Save compressed image to BytesIO
        output = io.BytesIO()
        image.save(output, format='JPEG', quality=quality, optimize=True)
        output.seek(0)

        # Calculate compression ratio
        original_size = file.tell() if hasattr(file, 'tell') else len(file.read())
        file.seek(0)
        compressed_size = len(output.getvalue())
        compression_ratio = (1 - compressed_size / original_size) * 100 if original_size > 0 else 0

        logger.info(f"Image compressed: {original_size} bytes -> {compressed_size} bytes ({compression_ratio:.1f}% reduction)")

        return output

    except Exception as e:
        logger.error(f"Image compression failed: {e}")
        # Return original file if compression fails
        file.seek(0)
        return file

def upload_images_to_cloudinary(files, property_id):
    """Upload compressed images to Cloudinary and return URLs"""
    image_urls = []

    for i, file in enumerate(files):
        try:
            # Compress image before upload
            compressed_file = compress_image(file)

            # Upload compressed image to Cloudinary
            result = cloudinary.uploader.upload(
                compressed_file,
                folder=f"properties/{property_id}",
                public_id=f"image_{i}_{int(datetime.now().timestamp())}",
                transformation=[
                    {'width': 800, 'height': 600, 'crop': 'fill', 'quality': 'auto'},
                    {'fetch_format': 'auto'}
                ],
                resource_type="auto",
                use_filename=False,
                unique_filename=True
            )
            image_urls.append(result['secure_url'])
            logger.info(f"Successfully uploaded compressed image {i} for property {property_id}")

        except Exception as e:
            logger.error(f"Failed to upload image {i}: {e}")
            # Try alternative upload method with original file
            try:
                file.seek(0)
                result = cloudinary.uploader.upload(
                    file,
                    folder=f"properties/{property_id}",
                    public_id=f"image_{i}_fallback_{int(datetime.now().timestamp())}",
                    resource_type="auto"
                )
                image_urls.append(result['secure_url'])
                logger.info(f"Successfully uploaded image {i} using fallback method")
            except Exception as e2:
                logger.error(f"Fallback upload also failed for image {i}: {e2}")
                continue

    return image_urls

# Template routes
@app.route('/')
def index():
    """Serve the main index page"""
    return render_template('index.html')

@app.route('/post-property')
def post_property_page():
    """Serve the post property page"""
    return render_template('post_property.html')

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """Handle admin login"""
    if request.method == 'POST':
        # Handle login form submission
        data = request.get_json() if request.is_json else request.form
        username = data.get('username', '')
        password = data.get('password', '')

        if check_admin_auth(username, password):
            session['admin_authenticated'] = True
            session['admin_user'] = username
            return jsonify({'success': True, 'redirect': '/admin/panel'})
        else:
            return jsonify({'success': False, 'error': 'Invalid credentials'}), 401

    # GET request - serve login page
    return render_template('admin_login.html')

@app.route('/admin/panel')
def admin_panel():
    """Serve the admin panel page"""
    # Check if user is authenticated via session
    if not session.get('admin_authenticated'):
        return redirect('/admin/login')
    return render_template('admin.html')

@app.route('/admin/logout')
def admin_logout():
    """Handle admin logout"""
    session.pop('admin_authenticated', None)
    session.pop('admin_user', None)
    return redirect('/admin/login')

# API Routes
@app.route('/api/properties', methods=['GET'])
def get_properties():
    """Get properties with filtering, pagination, and search"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)  # Max 50 per page

        # Filters
        city = request.args.get('city', '').strip()
        min_price = request.args.get('minPrice', type=float)
        max_price = request.args.get('maxPrice', type=float)
        search = request.args.get('search', '').strip()
        bedrooms = request.args.get('bedrooms', type=int)
        bathrooms = request.args.get('bathrooms', type=int)
        furnished = request.args.get('furnished', '').strip()

        # Build MongoDB filter
        filter_query = {'status': 'approved'}

        if city:
            filter_query['city'] = {'$regex': city, '$options': 'i'}

        if min_price is not None or max_price is not None:
            price_filter = {}
            if min_price is not None:
                price_filter['$gte'] = min_price
            if max_price is not None:
                price_filter['$lte'] = max_price
            filter_query['price'] = price_filter

        if search:
            filter_query['$text'] = {'$search': search}

        if bedrooms is not None:
            filter_query['bedrooms'] = bedrooms

        if bathrooms is not None:
            filter_query['bathrooms'] = bathrooms

        if furnished and furnished in ['furnished', 'semi', 'none']:
            filter_query['furnished_status'] = furnished

        # Calculate skip value
        skip = (page - 1) * per_page

        # Get total count
        total = properties_collection.count_documents(filter_query)

        # Get properties with pagination
        cursor = properties_collection.find(filter_query).skip(skip).limit(per_page)

        # Sort by creation date (newest first) or by text score if searching
        if search:
            cursor = cursor.sort([('score', {'$meta': 'textScore'}), ('created_at', -1)])
        else:
            cursor = cursor.sort('created_at', -1)

        properties = []
        for prop in cursor:
            prop['_id'] = str(prop['_id'])
            properties.append(prop)

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        logger.error(f"Error fetching properties: {e}")
        return jsonify({'success': False, 'error': 'Failed to fetch properties'}), 500

@app.route('/api/properties', methods=['POST'])
@limiter.limit("5 per minute")
def create_property():
    """Create a new property listing"""
    try:
        # Validate form data
        form_data = request.form.to_dict()
        is_valid, error_msg = validate_property_data(form_data)

        if not is_valid:
            return jsonify({'success': False, 'error': error_msg}), 400

        # Handle image uploads
        image_files = []
        for key in request.files:
            if key.startswith('image_'):
                file = request.files[key]
                if file and file.filename:
                    # Validate file type
                    if not file.content_type.startswith('image/'):
                        return jsonify({'success': False, 'error': f'Invalid file type: {file.filename}'}), 400

                    # Validate file size (5MB max per image)
                    file.seek(0, 2)  # Seek to end
                    file_size = file.tell()
                    file.seek(0)  # Reset to beginning

                    if file_size > 5 * 1024 * 1024:
                        return jsonify({'success': False, 'error': f'File too large: {file.filename}'}), 400

                    image_files.append(file)

        # Temporarily allow properties without images for testing
        # if not image_files:
        #     return jsonify({'success': False, 'error': 'At least one image is required'}), 400

        # Create property document
        property_doc = {
            'title': form_data['title'].strip(),
            'description': form_data['description'].strip(),
            'address': form_data['address'].strip(),
            'city': form_data['city'].strip(),
            'price': float(form_data['price']),
            'bedrooms': int(form_data['bedrooms']),
            'bathrooms': int(form_data['bathrooms']),
            'furnished_status': form_data.get('furnished_status', 'none'),
            'phone': re.sub(r'[^\d\+]', '', form_data['phone']),
            'amenities': form_data.get('amenities', '').split(',') if form_data.get('amenities') else [],
            'upvotes': 0,
            'downvotes': 0,
            'reports': [],
            'status': 'pending',
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc),
            'slug': generate_slug(form_data['title'])
        }

        # Insert property to get ID
        result = properties_collection.insert_one(property_doc)
        property_id = str(result.inserted_id)

        # Upload images to Cloudinary (if any)
        image_urls = []
        if image_files:
            try:
                image_urls = upload_images_to_cloudinary(image_files, property_id)
                if not image_urls:
                    logger.warning(f"No images uploaded successfully for property {property_id}")
            except Exception as e:
                logger.error(f"Error uploading images for property {property_id}: {e}")

        # Update property with image URLs (empty array if no images)
        properties_collection.update_one(
            {'_id': result.inserted_id},
            {'$set': {'image_urls': image_urls}}
        )

        logger.info(f"Property created successfully: {property_id}")

        return jsonify({
            'success': True,
            'property_id': property_id,
            'message': 'Property submitted successfully and is pending review'
        }), 201

    except Exception as e:
        logger.error(f"Error creating property: {e}")
        return jsonify({'success': False, 'error': 'Failed to create property'}), 500

@app.route('/api/vote', methods=['POST'])
@limiter.limit("5 per minute")
def vote_property():
    """Handle property voting"""
    try:
        data = request.get_json()

        if not data or 'property_id' not in data or 'vote_type' not in data:
            return jsonify({'success': False, 'error': 'Missing required fields'}), 400

        property_id = data['property_id']
        vote_type = data['vote_type']

        if vote_type not in ['up', 'down']:
            return jsonify({'success': False, 'error': 'Invalid vote type'}), 400

        try:
            object_id = ObjectId(property_id)
        except InvalidId:
            return jsonify({'success': False, 'error': 'Invalid property ID'}), 400

        # Update vote count atomically
        update_field = 'upvotes' if vote_type == 'up' else 'downvotes'
        result = properties_collection.update_one(
            {'_id': object_id, 'status': 'approved'},
            {'$inc': {update_field: 1}}
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found or not approved'}), 404

        # Get updated counts
        property_doc = properties_collection.find_one({'_id': object_id}, {'upvotes': 1, 'downvotes': 1})

        logger.info(f"Vote recorded: {property_id} - {vote_type}")

        return jsonify({
            'success': True,
            'upvotes': property_doc['upvotes'],
            'downvotes': property_doc['downvotes']
        })

    except Exception as e:
        logger.error(f"Error recording vote: {e}")
        return jsonify({'success': False, 'error': 'Failed to record vote'}), 500

@app.route('/api/report', methods=['POST'])
@limiter.limit("5 per minute")
def report_property():
    """Handle property reports"""
    try:
        data = request.get_json()

        if not data or 'property_id' not in data:
            return jsonify({'success': False, 'error': 'Missing property ID'}), 400

        property_id = data['property_id']
        reason = data.get('reason', 'User reported content')

        try:
            object_id = ObjectId(property_id)
        except InvalidId:
            return jsonify({'success': False, 'error': 'Invalid property ID'}), 400

        # Add report to property
        report_doc = {
            'reason': reason,
            'timestamp': datetime.now(timezone.utc),
            'ip_address': get_remote_address()
        }

        result = properties_collection.update_one(
            {'_id': object_id},
            {'$push': {'reports': report_doc}}
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Property reported: {property_id} - {reason}")

        return jsonify({
            'success': True,
            'message': 'Report submitted successfully'
        })

    except Exception as e:
        logger.error(f"Error submitting report: {e}")
        return jsonify({'success': False, 'error': 'Failed to submit report'}), 500

# Admin Panel Routes
@app.route('/admin')
@admin_required
def admin_dashboard():
    """Admin dashboard"""
    try:
        # Get pending properties count
        pending_count = properties_collection.count_documents({'status': 'pending'})

        # Get reported properties count
        reported_count = properties_collection.count_documents({'reports.0': {'$exists': True}})

        # Get total properties count
        total_count = properties_collection.count_documents({})

        # Get approved properties count
        approved_count = properties_collection.count_documents({'status': 'approved'})

        return jsonify({
            'success': True,
            'stats': {
                'pending': pending_count,
                'reported': reported_count,
                'total': total_count,
                'approved': approved_count
            }
        })

    except Exception as e:
        logger.error(f"Error loading admin dashboard: {e}")
        return jsonify({'success': False, 'error': 'Failed to load dashboard'}), 500

@app.route('/admin/properties/pending')
@admin_required
def admin_pending_properties():
    """Get pending properties for admin review"""
    try:
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 10)), 50)
        skip = (page - 1) * per_page

        # Get pending properties
        cursor = properties_collection.find({'status': 'pending'}).skip(skip).limit(per_page).sort('created_at', -1)

        properties = []
        for prop in cursor:
            prop['_id'] = str(prop['_id'])
            properties.append(prop)

        total = properties_collection.count_documents({'status': 'pending'})

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        logger.error(f"Error fetching pending properties: {e}")
        return jsonify({'success': False, 'error': 'Failed to fetch pending properties'}), 500

@app.route('/admin/properties/<property_id>/approve', methods=['POST'])
@admin_required
def admin_approve_property(property_id):
    """Approve a pending property"""
    try:
        object_id = ObjectId(property_id)

        result = properties_collection.update_one(
            {'_id': object_id, 'status': 'pending'},
            {
                '$set': {
                    'status': 'approved',
                    'updated_at': datetime.now(timezone.utc)
                }
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found or not pending'}), 404

        logger.info(f"Property approved: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property approved successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error approving property: {e}")
        return jsonify({'success': False, 'error': 'Failed to approve property'}), 500

@app.route('/admin/properties/<property_id>/reject', methods=['POST'])
@admin_required
def admin_reject_property(property_id):
    """Reject a pending property"""
    try:
        object_id = ObjectId(property_id)

        result = properties_collection.update_one(
            {'_id': object_id, 'status': 'pending'},
            {
                '$set': {
                    'status': 'rejected',
                    'updated_at': datetime.now(timezone.utc)
                }
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found or not pending'}), 404

        logger.info(f"Property rejected: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property rejected successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error rejecting property: {e}")
        return jsonify({'success': False, 'error': 'Failed to reject property'}), 500

@app.route('/admin/properties/reported')
@admin_required
def admin_reported_properties():
    """Get reported properties for admin review"""
    try:
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 10)), 50)
        skip = (page - 1) * per_page

        # Get properties with reports
        cursor = properties_collection.find({'reports.0': {'$exists': True}}).skip(skip).limit(per_page).sort('created_at', -1)

        properties = []
        for prop in cursor:
            prop['_id'] = str(prop['_id'])
            prop['report_count'] = len(prop.get('reports', []))
            properties.append(prop)

        total = properties_collection.count_documents({'reports.0': {'$exists': True}})

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        logger.error(f"Error fetching reported properties: {e}")
        return jsonify({'success': False, 'error': 'Failed to fetch reported properties'}), 500

@app.route('/admin/properties/<property_id>/dismiss-reports', methods=['POST'])
@admin_required
def admin_dismiss_reports(property_id):
    """Dismiss all reports for a property"""
    try:
        object_id = ObjectId(property_id)

        result = properties_collection.update_one(
            {'_id': object_id},
            {
                '$set': {
                    'reports': [],
                    'updated_at': datetime.now(timezone.utc)
                }
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Reports dismissed for property: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Reports dismissed successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error dismissing reports: {e}")
        return jsonify({'success': False, 'error': 'Failed to dismiss reports'}), 500

@app.route('/admin/properties/<property_id>/delete', methods=['DELETE'])
@admin_required
def admin_delete_property(property_id):
    """Delete a property"""
    try:
        object_id = ObjectId(property_id)

        # Get property to delete images from Cloudinary
        property_doc = properties_collection.find_one({'_id': object_id})
        if not property_doc:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        # Delete images from Cloudinary
        if property_doc.get('image_urls'):
            for image_url in property_doc['image_urls']:
                try:
                    # Extract public_id from URL
                    public_id = image_url.split('/')[-1].split('.')[0]
                    cloudinary.uploader.destroy(f"properties/{property_id}/{public_id}")
                except Exception as e:
                    logger.warning(f"Failed to delete image from Cloudinary: {e}")

        # Delete property from database
        result = properties_collection.delete_one({'_id': object_id})

        if result.deleted_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Property deleted: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property deleted successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error deleting property: {e}")
        return jsonify({'success': False, 'error': 'Failed to delete property'}), 500

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.errorhandler(413)
def too_large(error):
    return jsonify({'success': False, 'error': 'File too large'}), 413

# Health check endpoint
@app.route('/health')
def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        db.command('ping')
        return jsonify({
            'success': True,
            'status': 'healthy',
            'database': 'connected',
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'database': 'disconnected',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 503

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_ENV') == 'development'

    logger.info(f"Starting HavenHuts server on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)