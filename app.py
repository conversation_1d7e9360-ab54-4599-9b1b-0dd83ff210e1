import os
import logging
from datetime import datetime, timezone
from functools import wraps
from bson import ObjectId
from bson.errors import InvalidId
import cloudinary
import cloudinary.uploader
from flask import Flask, request, jsonify, render_template, abort
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from pymongo import MongoClient, ASCENDING, TEXT
from dotenv import load_dotenv
import re

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize CORS
CORS(app, origins=os.getenv('ALLOWED_ORIGINS', '*').split(','))

# Initialize rate limiter
limiter = Limiter(
    key_func=get_remote_address,
    app=app,
    default_limits=["200 per day", "50 per hour"]
)

# Configure Cloudinary
cloudinary.config(
    cloud_name=os.getenv('CLOUDINARY_CLOUD_NAME'),
    api_key=os.getenv('CLOUDINARY_API_KEY'),
    api_secret=os.getenv('CLOUDINARY_API_SECRET'),
    secure=True
)

# MongoDB connection
try:
    client = MongoClient(os.getenv('MONGO_URI', 'mongodb://localhost:27017/'))
    db = client[os.getenv('MONGO_DB_NAME', 'eznest')]
    properties_collection = db.properties

    # Create indexes for better performance
    properties_collection.create_index([("title", TEXT), ("description", TEXT)])
    properties_collection.create_index("city")
    properties_collection.create_index("price")
    properties_collection.create_index("bedrooms")
    properties_collection.create_index("furnished_status")
    properties_collection.create_index("status")
    properties_collection.create_index("created_at")

    logger.info("MongoDB connected and indexes created successfully")
except Exception as e:
    logger.error(f"MongoDB connection failed: {e}")
    db = None

# Admin authentication decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth = request.authorization
        admin_user = os.getenv('ADMIN_USER', 'admin')
        admin_pass = os.getenv('ADMIN_PASS', 'admin123')

        if not auth or auth.username != admin_user or auth.password != admin_pass:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function

# Utility functions
def validate_property_data(data):
    """Validate property form data"""
    required_fields = ['title', 'address', 'city', 'price', 'bedrooms', 'bathrooms', 'description', 'phone']

    for field in required_fields:
        if not data.get(field):
            return False, f"Missing required field: {field}"

    try:
        price = float(data['price'])
        bedrooms = int(data['bedrooms'])
        bathrooms = int(data['bathrooms'])

        if price <= 0 or bedrooms <= 0 or bathrooms <= 0:
            return False, "Price, bedrooms, and bathrooms must be positive numbers"
    except (ValueError, TypeError):
        return False, "Invalid numeric values for price, bedrooms, or bathrooms"

    if len(data['description']) < 100:
        return False, "Description must be at least 100 characters long"

    # Validate phone number (basic validation)
    phone_pattern = re.compile(r'^[\+]?[1-9][\d]{0,15}$')
    clean_phone = re.sub(r'[^\d\+]', '', data['phone'])
    if not phone_pattern.match(clean_phone):
        return False, "Invalid phone number format"

    return True, None

def generate_slug(title):
    """Generate SEO-friendly slug from title"""
    slug = re.sub(r'[^a-zA-Z0-9\s-]', '', title.lower())
    slug = re.sub(r'\s+', '-', slug)
    slug = re.sub(r'-+', '-', slug)
    return slug.strip('-')

def upload_images_to_cloudinary(files, property_id):
    """Upload images to Cloudinary and return URLs"""
    image_urls = []

    for i, file in enumerate(files):
        try:
            # Reset file pointer to beginning
            file.seek(0)

            # Upload to Cloudinary using unsigned upload (no timestamp required)
            # This bypasses the timestamp validation issue
            result = cloudinary.uploader.upload(
                file,
                folder=f"properties/{property_id}",
                public_id=f"image_{i}_{int(datetime.now().timestamp())}",
                transformation=[
                    {'width': 800, 'height': 600, 'crop': 'fill', 'quality': 'auto'},
                    {'fetch_format': 'auto'}
                ],
                resource_type="auto",
                use_filename=False,
                unique_filename=True
            )
            image_urls.append(result['secure_url'])
            logger.info(f"Successfully uploaded image {i} for property {property_id}")
        except Exception as e:
            logger.error(f"Failed to upload image {i}: {e}")
            # Try alternative upload method without transformations
            try:
                file.seek(0)
                result = cloudinary.uploader.upload(
                    file,
                    folder=f"properties/{property_id}",
                    public_id=f"image_{i}_simple_{int(datetime.now().timestamp())}",
                    resource_type="auto"
                )
                image_urls.append(result['secure_url'])
                logger.info(f"Successfully uploaded image {i} using fallback method")
            except Exception as e2:
                logger.error(f"Fallback upload also failed for image {i}: {e2}")
                continue

    return image_urls

# Template routes
@app.route('/')
def index():
    """Serve the main index page"""
    return render_template('index.html')

@app.route('/post-property')
def post_property_page():
    """Serve the post property page"""
    return render_template('post_property.html')

@app.route('/admin/panel')
@admin_required
def admin_panel():
    """Serve the admin panel page"""
    return render_template('admin.html')

# API Routes
@app.route('/api/properties', methods=['GET'])
def get_properties():
    """Get properties with filtering, pagination, and search"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)  # Max 50 per page

        # Filters
        city = request.args.get('city', '').strip()
        min_price = request.args.get('minPrice', type=float)
        max_price = request.args.get('maxPrice', type=float)
        search = request.args.get('search', '').strip()
        bedrooms = request.args.get('bedrooms', type=int)
        bathrooms = request.args.get('bathrooms', type=int)
        furnished = request.args.get('furnished', '').strip()

        # Build MongoDB filter
        filter_query = {'status': 'approved'}

        if city:
            filter_query['city'] = {'$regex': city, '$options': 'i'}

        if min_price is not None or max_price is not None:
            price_filter = {}
            if min_price is not None:
                price_filter['$gte'] = min_price
            if max_price is not None:
                price_filter['$lte'] = max_price
            filter_query['price'] = price_filter

        if search:
            filter_query['$text'] = {'$search': search}

        if bedrooms is not None:
            filter_query['bedrooms'] = bedrooms

        if bathrooms is not None:
            filter_query['bathrooms'] = bathrooms

        if furnished and furnished in ['furnished', 'semi', 'none']:
            filter_query['furnished_status'] = furnished

        # Calculate skip value
        skip = (page - 1) * per_page

        # Get total count
        total = properties_collection.count_documents(filter_query)

        # Get properties with pagination
        cursor = properties_collection.find(filter_query).skip(skip).limit(per_page)

        # Sort by creation date (newest first) or by text score if searching
        if search:
            cursor = cursor.sort([('score', {'$meta': 'textScore'}), ('created_at', -1)])
        else:
            cursor = cursor.sort('created_at', -1)

        properties = []
        for prop in cursor:
            prop['_id'] = str(prop['_id'])
            properties.append(prop)

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        logger.error(f"Error fetching properties: {e}")
        return jsonify({'success': False, 'error': 'Failed to fetch properties'}), 500

@app.route('/api/properties', methods=['POST'])
@limiter.limit("5 per minute")
def create_property():
    """Create a new property listing"""
    try:
        # Validate form data
        form_data = request.form.to_dict()
        is_valid, error_msg = validate_property_data(form_data)

        if not is_valid:
            return jsonify({'success': False, 'error': error_msg}), 400

        # Handle image uploads
        image_files = []
        for key in request.files:
            if key.startswith('image_'):
                file = request.files[key]
                if file and file.filename:
                    # Validate file type
                    if not file.content_type.startswith('image/'):
                        return jsonify({'success': False, 'error': f'Invalid file type: {file.filename}'}), 400

                    # Validate file size (5MB max per image)
                    file.seek(0, 2)  # Seek to end
                    file_size = file.tell()
                    file.seek(0)  # Reset to beginning

                    if file_size > 5 * 1024 * 1024:
                        return jsonify({'success': False, 'error': f'File too large: {file.filename}'}), 400

                    image_files.append(file)

        # Temporarily allow properties without images for testing
        # if not image_files:
        #     return jsonify({'success': False, 'error': 'At least one image is required'}), 400

        # Create property document
        property_doc = {
            'title': form_data['title'].strip(),
            'description': form_data['description'].strip(),
            'address': form_data['address'].strip(),
            'city': form_data['city'].strip(),
            'price': float(form_data['price']),
            'bedrooms': int(form_data['bedrooms']),
            'bathrooms': int(form_data['bathrooms']),
            'furnished_status': form_data.get('furnished_status', 'none'),
            'phone': re.sub(r'[^\d\+]', '', form_data['phone']),
            'amenities': form_data.get('amenities', '').split(',') if form_data.get('amenities') else [],
            'upvotes': 0,
            'downvotes': 0,
            'reports': [],
            'status': 'pending',
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc),
            'slug': generate_slug(form_data['title'])
        }

        # Insert property to get ID
        result = properties_collection.insert_one(property_doc)
        property_id = str(result.inserted_id)

        # Upload images to Cloudinary (if any)
        image_urls = []
        if image_files:
            try:
                image_urls = upload_images_to_cloudinary(image_files, property_id)
                if not image_urls:
                    logger.warning(f"No images uploaded successfully for property {property_id}")
            except Exception as e:
                logger.error(f"Error uploading images for property {property_id}: {e}")

        # Update property with image URLs (empty array if no images)
        properties_collection.update_one(
            {'_id': result.inserted_id},
            {'$set': {'image_urls': image_urls}}
        )

        logger.info(f"Property created successfully: {property_id}")

        return jsonify({
            'success': True,
            'property_id': property_id,
            'message': 'Property submitted successfully and is pending review'
        }), 201

    except Exception as e:
        logger.error(f"Error creating property: {e}")
        return jsonify({'success': False, 'error': 'Failed to create property'}), 500

@app.route('/api/vote', methods=['POST'])
@limiter.limit("5 per minute")
def vote_property():
    """Handle property voting"""
    try:
        data = request.get_json()

        if not data or 'property_id' not in data or 'vote_type' not in data:
            return jsonify({'success': False, 'error': 'Missing required fields'}), 400

        property_id = data['property_id']
        vote_type = data['vote_type']

        if vote_type not in ['up', 'down']:
            return jsonify({'success': False, 'error': 'Invalid vote type'}), 400

        try:
            object_id = ObjectId(property_id)
        except InvalidId:
            return jsonify({'success': False, 'error': 'Invalid property ID'}), 400

        # Update vote count atomically
        update_field = 'upvotes' if vote_type == 'up' else 'downvotes'
        result = properties_collection.update_one(
            {'_id': object_id, 'status': 'approved'},
            {'$inc': {update_field: 1}}
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found or not approved'}), 404

        # Get updated counts
        property_doc = properties_collection.find_one({'_id': object_id}, {'upvotes': 1, 'downvotes': 1})

        logger.info(f"Vote recorded: {property_id} - {vote_type}")

        return jsonify({
            'success': True,
            'upvotes': property_doc['upvotes'],
            'downvotes': property_doc['downvotes']
        })

    except Exception as e:
        logger.error(f"Error recording vote: {e}")
        return jsonify({'success': False, 'error': 'Failed to record vote'}), 500

@app.route('/api/report', methods=['POST'])
@limiter.limit("5 per minute")
def report_property():
    """Handle property reports"""
    try:
        data = request.get_json()

        if not data or 'property_id' not in data:
            return jsonify({'success': False, 'error': 'Missing property ID'}), 400

        property_id = data['property_id']
        reason = data.get('reason', 'User reported content')

        try:
            object_id = ObjectId(property_id)
        except InvalidId:
            return jsonify({'success': False, 'error': 'Invalid property ID'}), 400

        # Add report to property
        report_doc = {
            'reason': reason,
            'timestamp': datetime.now(timezone.utc),
            'ip_address': get_remote_address()
        }

        result = properties_collection.update_one(
            {'_id': object_id},
            {'$push': {'reports': report_doc}}
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Property reported: {property_id} - {reason}")

        return jsonify({
            'success': True,
            'message': 'Report submitted successfully'
        })

    except Exception as e:
        logger.error(f"Error submitting report: {e}")
        return jsonify({'success': False, 'error': 'Failed to submit report'}), 500

# Admin Panel Routes
@app.route('/admin')
@admin_required
def admin_dashboard():
    """Admin dashboard"""
    try:
        # Get pending properties count
        pending_count = properties_collection.count_documents({'status': 'pending'})

        # Get reported properties count
        reported_count = properties_collection.count_documents({'reports.0': {'$exists': True}})

        # Get total properties count
        total_count = properties_collection.count_documents({})

        # Get approved properties count
        approved_count = properties_collection.count_documents({'status': 'approved'})

        return jsonify({
            'success': True,
            'stats': {
                'pending': pending_count,
                'reported': reported_count,
                'total': total_count,
                'approved': approved_count
            }
        })

    except Exception as e:
        logger.error(f"Error loading admin dashboard: {e}")
        return jsonify({'success': False, 'error': 'Failed to load dashboard'}), 500

@app.route('/admin/properties/pending')
@admin_required
def admin_pending_properties():
    """Get pending properties for admin review"""
    try:
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 10)), 50)
        skip = (page - 1) * per_page

        # Get pending properties
        cursor = properties_collection.find({'status': 'pending'}).skip(skip).limit(per_page).sort('created_at', -1)

        properties = []
        for prop in cursor:
            prop['_id'] = str(prop['_id'])
            properties.append(prop)

        total = properties_collection.count_documents({'status': 'pending'})

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        logger.error(f"Error fetching pending properties: {e}")
        return jsonify({'success': False, 'error': 'Failed to fetch pending properties'}), 500

@app.route('/admin/properties/<property_id>/approve', methods=['POST'])
@admin_required
def admin_approve_property(property_id):
    """Approve a pending property"""
    try:
        object_id = ObjectId(property_id)

        result = properties_collection.update_one(
            {'_id': object_id, 'status': 'pending'},
            {
                '$set': {
                    'status': 'approved',
                    'updated_at': datetime.now(timezone.utc)
                }
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found or not pending'}), 404

        logger.info(f"Property approved: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property approved successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error approving property: {e}")
        return jsonify({'success': False, 'error': 'Failed to approve property'}), 500

@app.route('/admin/properties/<property_id>/reject', methods=['POST'])
@admin_required
def admin_reject_property(property_id):
    """Reject a pending property"""
    try:
        object_id = ObjectId(property_id)

        result = properties_collection.update_one(
            {'_id': object_id, 'status': 'pending'},
            {
                '$set': {
                    'status': 'rejected',
                    'updated_at': datetime.now(timezone.utc)
                }
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found or not pending'}), 404

        logger.info(f"Property rejected: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property rejected successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error rejecting property: {e}")
        return jsonify({'success': False, 'error': 'Failed to reject property'}), 500

@app.route('/admin/properties/reported')
@admin_required
def admin_reported_properties():
    """Get reported properties for admin review"""
    try:
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 10)), 50)
        skip = (page - 1) * per_page

        # Get properties with reports
        cursor = properties_collection.find({'reports.0': {'$exists': True}}).skip(skip).limit(per_page).sort('created_at', -1)

        properties = []
        for prop in cursor:
            prop['_id'] = str(prop['_id'])
            prop['report_count'] = len(prop.get('reports', []))
            properties.append(prop)

        total = properties_collection.count_documents({'reports.0': {'$exists': True}})

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        logger.error(f"Error fetching reported properties: {e}")
        return jsonify({'success': False, 'error': 'Failed to fetch reported properties'}), 500

@app.route('/admin/properties/<property_id>/dismiss-reports', methods=['POST'])
@admin_required
def admin_dismiss_reports(property_id):
    """Dismiss all reports for a property"""
    try:
        object_id = ObjectId(property_id)

        result = properties_collection.update_one(
            {'_id': object_id},
            {
                '$set': {
                    'reports': [],
                    'updated_at': datetime.now(timezone.utc)
                }
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Reports dismissed for property: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Reports dismissed successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error dismissing reports: {e}")
        return jsonify({'success': False, 'error': 'Failed to dismiss reports'}), 500

@app.route('/admin/properties/<property_id>/delete', methods=['DELETE'])
@admin_required
def admin_delete_property(property_id):
    """Delete a property"""
    try:
        object_id = ObjectId(property_id)

        # Get property to delete images from Cloudinary
        property_doc = properties_collection.find_one({'_id': object_id})
        if not property_doc:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        # Delete images from Cloudinary
        if property_doc.get('image_urls'):
            for image_url in property_doc['image_urls']:
                try:
                    # Extract public_id from URL
                    public_id = image_url.split('/')[-1].split('.')[0]
                    cloudinary.uploader.destroy(f"properties/{property_id}/{public_id}")
                except Exception as e:
                    logger.warning(f"Failed to delete image from Cloudinary: {e}")

        # Delete property from database
        result = properties_collection.delete_one({'_id': object_id})

        if result.deleted_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Property deleted: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property deleted successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error deleting property: {e}")
        return jsonify({'success': False, 'error': 'Failed to delete property'}), 500

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.errorhandler(413)
def too_large(error):
    return jsonify({'success': False, 'error': 'File too large'}), 413

# Health check endpoint
@app.route('/health')
def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        db.command('ping')
        return jsonify({
            'success': True,
            'status': 'healthy',
            'database': 'connected',
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'database': 'disconnected',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 503

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_ENV') == 'development'

    logger.info(f"Starting HavenHuts server on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)