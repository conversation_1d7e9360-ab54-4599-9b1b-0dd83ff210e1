#!/usr/bin/env python3
"""
Comprehensive test for all HavenHuts improvements:
1. Fixed photo swiping functionality
2. Kolkata-focused post property form
3. Security and DDoS protection
4. Image compression
5. Currency formatting (₹)
"""

import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor

def test_photo_gallery():
    """Test that photo gallery JavaScript is properly implemented"""
    print("🧪 Testing Photo Gallery Implementation...")
    
    try:
        response = requests.get('http://localhost:5000/', timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Check for gallery functions
            gallery_functions = [
                'createImageGallery',
                'nextImage',
                'previousImage', 
                'goToImage',
                'updateGallery',
                'data-current-index'
            ]
            
            found_functions = 0
            for func in gallery_functions:
                if func in content:
                    found_functions += 1
            
            if found_functions >= 5:
                print("   ✅ Photo gallery functions implemented")
                return True
            else:
                print(f"   ❌ Missing gallery functions: {found_functions}/{len(gallery_functions)}")
                return False
        else:
            print(f"   ❌ Homepage not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing gallery: {e}")
        return False

def test_kolkata_form():
    """Test Kolkata-focused post property form"""
    print("\n🧪 Testing Kolkata-Focused Form...")
    
    try:
        response = requests.get('http://localhost:5000/post-property', timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Check for Kolkata-specific elements
            kolkata_elements = [
                'Kolkata',
                'WhatsApp Number',
                '+91 98765 43210',
                'Spacious 2BHK Flat in Salt Lake'
            ]
            
            found_elements = 0
            for element in kolkata_elements:
                if element in content:
                    found_elements += 1
            
            # Check that optional fields are removed
            optional_fields = ['bedrooms', 'bathrooms', 'furnished_status', 'description', 'amenities']
            removed_fields = 0
            for field in optional_fields:
                if f'name="{field}"' not in content:
                    removed_fields += 1
            
            if found_elements >= 3 and removed_fields >= 3:
                print("   ✅ Form is Kolkata-focused and simplified")
                return True
            else:
                print(f"   ⚠️  Form needs more Kolkata customization: {found_elements}/{len(kolkata_elements)} elements, {removed_fields}/{len(optional_fields)} fields removed")
                return False
        else:
            print(f"   ❌ Post property form not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing form: {e}")
        return False

def test_security_features():
    """Test security and DDoS protection"""
    print("\n🧪 Testing Security Features...")
    
    # Test 1: Rate limiting
    print("   Testing rate limiting...")
    try:
        # Make multiple rapid requests
        responses = []
        for i in range(25):  # Should trigger rate limiting
            response = requests.get('http://localhost:5000/api/properties', timeout=5)
            responses.append(response.status_code)
            time.sleep(0.1)  # Small delay
        
        # Check if any requests were rate limited (429)
        rate_limited = any(status == 429 for status in responses)
        if rate_limited:
            print("   ✅ Rate limiting is working")
        else:
            print("   ⚠️  Rate limiting may not be aggressive enough")
        
    except Exception as e:
        print(f"   ❌ Error testing rate limiting: {e}")
        rate_limited = False
    
    # Test 2: SQL injection protection
    print("   Testing SQL injection protection...")
    try:
        malicious_params = {
            'search': "'; DROP TABLE properties; --",
            'city': "1=1 OR 1=1",
            'minPrice': "UNION SELECT * FROM users"
        }
        
        response = requests.get('http://localhost:5000/api/properties', params=malicious_params, timeout=10)
        
        if response.status_code == 403:
            print("   ✅ SQL injection protection working")
            injection_protected = True
        elif response.status_code == 200:
            print("   ⚠️  SQL injection protection may need improvement")
            injection_protected = False
        else:
            print(f"   ⚠️  Unexpected response to injection test: {response.status_code}")
            injection_protected = False
            
    except Exception as e:
        print(f"   ❌ Error testing injection protection: {e}")
        injection_protected = False
    
    # Test 3: Suspicious user agent blocking
    print("   Testing user agent filtering...")
    try:
        headers = {'User-Agent': 'sqlmap/1.0 (http://sqlmap.org)'}
        response = requests.get('http://localhost:5000/', headers=headers, timeout=10)
        
        if response.status_code == 403:
            print("   ✅ Suspicious user agent blocking working")
            agent_blocked = True
        else:
            print("   ⚠️  User agent filtering may need improvement")
            agent_blocked = False
            
    except Exception as e:
        print(f"   ❌ Error testing user agent filtering: {e}")
        agent_blocked = False
    
    return rate_limited or injection_protected or agent_blocked

def test_currency_formatting():
    """Test Indian Rupee currency formatting"""
    print("\n🧪 Testing Currency Formatting...")
    
    try:
        response = requests.get('http://localhost:5000/', timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Check for rupee symbol and formatting function
            currency_features = [
                '₹',
                'formatIndianCurrency',
                'Monthly Rent (₹)'
            ]
            
            found_features = 0
            for feature in currency_features:
                if feature in content:
                    found_features += 1
            
            if found_features >= 2:
                print("   ✅ Currency formatting implemented")
                return True
            else:
                print(f"   ❌ Currency formatting incomplete: {found_features}/{len(currency_features)}")
                return False
        else:
            print(f"   ❌ Homepage not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing currency: {e}")
        return False

def test_simplified_property_creation():
    """Test simplified property creation with only required fields"""
    print("\n🧪 Testing Simplified Property Creation...")
    
    try:
        # Test with minimal required data
        property_data = {
            'title': 'Beautiful 2BHK Apartment in Park Street, Kolkata',
            'price': '25000',
            'city': 'Kolkata',
            'phone': '+91 98765 43210'
        }
        
        response = requests.post(
            'http://localhost:5000/api/properties',
            data=property_data,
            timeout=15
        )
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print("   ✅ Simplified property creation working")
                return True
            else:
                print(f"   ❌ Property creation failed: {result.get('error')}")
                return False
        else:
            print(f"   ❌ Property creation request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Response text: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing property creation: {e}")
        return False

def test_ddos_protection():
    """Test DDoS protection with concurrent requests"""
    print("\n🧪 Testing DDoS Protection...")
    
    def make_request():
        try:
            response = requests.get('http://localhost:5000/api/properties', timeout=5)
            return response.status_code
        except:
            return 0
    
    try:
        # Make 50 concurrent requests to trigger DDoS protection
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(make_request) for _ in range(50)]
            results = [future.result() for future in futures]
        
        # Count blocked requests (429 status)
        blocked_count = results.count(429)
        success_count = results.count(200)
        
        print(f"   Requests: {success_count} successful, {blocked_count} blocked")
        
        if blocked_count > 0:
            print("   ✅ DDoS protection is working")
            return True
        else:
            print("   ⚠️  DDoS protection may need tuning")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing DDoS protection: {e}")
        return False

def main():
    """Run all improvement tests"""
    print("🧪 HavenHuts Final Improvements Test Suite")
    print("=" * 60)
    
    # Test all improvements
    gallery_test = test_photo_gallery()
    kolkata_test = test_kolkata_form()
    security_test = test_security_features()
    currency_test = test_currency_formatting()
    creation_test = test_simplified_property_creation()
    ddos_test = test_ddos_protection()
    
    print("\n" + "=" * 60)
    print("📊 Final Test Results:")
    print(f"   Photo Gallery Fix: {'✅ PASSED' if gallery_test else '❌ FAILED'}")
    print(f"   Kolkata-Focused Form: {'✅ PASSED' if kolkata_test else '❌ FAILED'}")
    print(f"   Security Features: {'✅ PASSED' if security_test else '❌ FAILED'}")
    print(f"   Currency Formatting: {'✅ PASSED' if currency_test else '❌ FAILED'}")
    print(f"   Simplified Creation: {'✅ PASSED' if creation_test else '❌ FAILED'}")
    print(f"   DDoS Protection: {'✅ PASSED' if ddos_test else '❌ FAILED'}")
    
    all_passed = all([gallery_test, kolkata_test, security_test, currency_test, creation_test, ddos_test])
    
    if all_passed:
        print("\n🎉 ALL IMPROVEMENTS SUCCESSFULLY IMPLEMENTED!")
        print("\n✅ Your HavenHuts platform now has:")
        print("   • Fixed swipeable photo galleries")
        print("   • Kolkata-focused simplified property form")
        print("   • Comprehensive security and DDoS protection")
        print("   • Indian Rupee currency formatting")
        print("   • Streamlined user experience")
        print("   • Production-ready security features")
        
        print("\n🚀 Ready for Kolkata market deployment!")
        
    else:
        print("\n⚠️  Some improvements need attention. Check the details above.")
    
    return all_passed

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
