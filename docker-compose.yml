version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:5.0
    container_name: havenhuts_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: havenhuts
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - havenhuts_network

  # HavenHuts Backend
  havenhuts_backend:
    build: .
    container_name: havenhuts_backend
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - MONGO_URI=********************************************************************
      - SECRET_KEY=dev-secret-key-change-in-production
      - ADMIN_USER=admin
      - ADMIN_PASS=admin123
      # Add your Cloudinary credentials here
      - CLOUDINARY_CLOUD_NAME=${CLOUDINARY_CLOUD_NAME}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}
    depends_on:
      - mongodb
    networks:
      - havenhuts_network
    volumes:
      - .:/app
    command: python run.py

  # Optional: MongoDB Express for database management
  mongo_express:
    image: mongo-express:latest
    container_name: havenhuts_mongo_express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      - mongodb
    networks:
      - havenhuts_network

volumes:
  mongodb_data:

networks:
  havenhuts_network:
    driver: bridge
