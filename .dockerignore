# Git
.git
.gitignore
README.md
DEPLOYMENT.md

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Development files
.env
.env.local
.env.development
.env.test
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test files
test_*.py
*_test.py
tests/
test/

# Documentation
docs/
*.md
!DEPLOYMENT.md

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# Build artifacts
build/
dist/
*.egg-info/

# Deployment files (keep only what's needed)
.github/
deploy.tar
*.tar
*.zip
