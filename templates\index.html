<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HavenHuts - Find Your Perfect Room Rental | Premium Accommodations</title>
    <meta name="description" content="Discover premium room rentals and accommodations on HavenHuts. Browse verified properties with detailed photos, amenities, and instant WhatsApp contact. Find your perfect home today.">
    <meta name="keywords" content="room rental, accommodation, apartment, housing, rent, property, HavenHuts">
    <meta name="author" content="HavenHuts">
    <meta property="og:title" content="HavenHuts - Find Your Perfect Room Rental">
    <meta property="og:description" content="Discover premium room rentals and accommodations with verified properties and instant contact.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://havenhuts.com">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://havenhuts.com">

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .property-card {
            transition: all 0.3s ease;
        }
        .property-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .modal-overlay {
            backdrop-filter: blur(4px);
        }
        .scrollable-description {
            max-height: 120px;
            overflow-y: auto;
        }
        .scrollable-description::-webkit-scrollbar {
            width: 4px;
        }
        .scrollable-description::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        .scrollable-description::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        .mobile-menu {
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .mobile-menu.open {
            transform: translateX(0);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-primary">
                        <i class="fas fa-home mr-2"></i>HavenHuts
                    </h1>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/" class="text-gray-700 hover:text-primary transition-colors font-medium">Home</a>
                    <a href="/post-property" class="text-gray-700 hover:text-primary transition-colors font-medium">Post Property</a>
                    <a href="#" class="text-gray-700 hover:text-primary transition-colors font-medium">About</a>
                    <a href="#" class="text-gray-700 hover:text-primary transition-colors font-medium">Contact</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobileMenuBtn" class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bars text-gray-700 text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobileMenu" class="mobile-menu fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-50 md:hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-8">
                    <h2 class="text-xl font-bold text-primary">Menu</h2>
                    <button id="closeMobileMenu" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-times text-gray-700 text-xl"></i>
                    </button>
                </div>
                <nav class="space-y-4">
                    <a href="/" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-home mr-3"></i>Home
                    </a>
                    <a href="/post-property" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-plus mr-3"></i>Post Property
                    </a>
                    <a href="#" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-info-circle mr-3"></i>About
                    </a>
                    <a href="#" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-envelope mr-3"></i>Contact
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-primary to-purple-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Find Your Perfect Room</h1>
            <p class="text-xl md:text-2xl mb-8 text-purple-100">Discover comfortable and affordable accommodations</p>

            <!-- Search Bar -->
            <div class="max-w-4xl mx-auto bg-white rounded-2xl p-6 shadow-2xl">
                <div class="flex flex-col lg:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" id="searchInput" placeholder="Search by location, title..."
                                   class="w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-gray-900 text-lg">
                        </div>
                    </div>
                    <button onclick="searchProperties()"
                            class="px-8 py-4 bg-primary text-white rounded-xl hover:bg-purple-700 transition-colors font-semibold text-lg">
                        Search Properties
                    </button>
                </div>

                <!-- Filters -->
                <div class="flex flex-wrap gap-4 justify-center mt-6">
                    <select id="priceFilter" onchange="filterProperties()"
                            class="px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary text-gray-900">
                        <option value="recommended">Recommended</option>
                        <option value="low-to-high">Price: Low to High</option>
                        <option value="high-to-low">Price: High to Low</option>
                    </select>
                    <select id="locationFilter" onchange="filterProperties()"
                            class="px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary text-gray-900">
                        <option value="">All Locations</option>
                        <option value="downtown">Downtown</option>
                        <option value="suburbs">Suburbs</option>
                        <option value="university">University Area</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Properties Grid -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Properties</h2>
                <p class="text-xl text-gray-600">Handpicked accommodations for your comfort</p>
            </div>

            <div id="propertiesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Property cards will be dynamically generated here -->
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-12 hidden">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                <p class="mt-4 text-gray-600 text-lg">Loading amazing properties...</p>
            </div>

            <!-- No Results State -->
            <div id="noResultsState" class="text-center py-16 hidden">
                <i class="fas fa-search text-6xl text-gray-300 mb-6"></i>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">No Properties Found</h3>
                <p class="text-gray-600 text-lg">Try adjusting your search criteria or browse all properties.</p>
            </div>
        </div>
    </section>

    <!-- Property Details Modal -->
    <div id="propertyModal" class="fixed inset-0 bg-black bg-opacity-50 modal-overlay flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Property Details</h2>
                    <button onclick="closeModal()" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                        <i class="fas fa-times text-gray-500 text-xl"></i>
                    </button>
                </div>

                <div id="modalContent">
                    <!-- Modal content will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <h3 class="text-2xl font-bold mb-4 text-primary">
                        <i class="fas fa-home mr-2"></i>HavenHuts
                    </h3>
                    <p class="text-gray-300 text-lg mb-6">Your trusted platform for finding the perfect room rental. We connect property owners with tenants looking for quality accommodations.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="bg-gray-800 p-3 rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="bg-gray-800 p-3 rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="bg-gray-800 p-3 rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h4 class="font-bold mb-6 text-lg">Quick Links</h4>
                    <ul class="space-y-3 text-gray-300">
                        <li><a href="/" class="hover:text-white transition-colors">Home</a></li>
                        <li><a href="/post-property" class="hover:text-white transition-colors">Post Property</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold mb-6 text-lg">Support</h4>
                    <ul class="space-y-3 text-gray-300">
                        <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Safety Guidelines</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Terms of Service</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
                <p>&copy; 2024 HavenHuts. All rights reserved. | Designed for modern living.</p>
            </div>
        </div>
    </footer>

    <script>
                slug: "modern-loft-suburbs-austin",

        let filteredProperties = [...properties];

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            initializeMobileMenu();
            initializeSearch();
            // Load properties from backend instead of using sample data
            loadPropertiesFromBackend();
        });

        // Mobile menu functionality
        function initializeMobileMenu() {
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');

            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.add('open');
                document.body.style.overflow = 'hidden';
            });

            closeMobileMenu.addEventListener('click', () => {
                mobileMenu.classList.remove('open');
                document.body.style.overflow = 'auto';
            });

            // Close menu when clicking outside
            mobileMenu.addEventListener('click', (e) => {
                if (e.target === mobileMenu) {
                    mobileMenu.classList.remove('open');
                    document.body.style.overflow = 'auto';
                }
            });
        }

        // Render properties with modern card design
        function renderProperties() {
            const grid = document.getElementById('propertiesGrid');
            const loadingState = document.getElementById('loadingState');
            const noResultsState = document.getElementById('noResultsState');

            if (filteredProperties.length === 0) {
                grid.innerHTML = '';
                loadingState.classList.add('hidden');
                noResultsState.classList.remove('hidden');
                return;
            }

            noResultsState.classList.add('hidden');
            loadingState.classList.add('hidden');

            grid.innerHTML = filteredProperties.map(property => `
                <article class="property-card bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
                         itemscope itemtype="https://schema.org/Accommodation">
                    <div class="relative">
                        <img src="${property.image_urls && property.image_urls[0] ? property.image_urls[0] : '/placeholder.svg?height=300&width=400'}"
                             alt="${property.title} - ${property.city}"
                             class="w-full h-64 object-cover"
                             itemprop="image">
                        <button onclick="reportProperty('${property._id}')"
                                class="absolute top-4 right-4 bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100 transition-all shadow-lg"
                                title="Report this property">
                            <i class="fas fa-flag text-red-500"></i>
                        </button>
                        ${property.featured ? '<div class="absolute top-4 left-4 bg-accent text-white px-3 py-1 rounded-full text-sm font-semibold">Featured</div>' : ''}
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2" itemprop="name">${property.title}</h3>
                        <p class="text-gray-600 mb-3 flex items-center" itemprop="address">
                            <i class="fas fa-map-marker-alt mr-2 text-primary"></i>${property.city}
                        </p>
                        <p class="text-3xl font-bold text-primary mb-4" itemprop="priceRange">$${property.price}<span class="text-lg text-gray-500">/month</span></p>

                        <!-- Action Buttons -->
                        <div class="space-y-3 mb-4">
                            <button onclick="openModal('${property._id}')"
                                    class="w-full bg-primary text-white py-3 px-4 rounded-xl hover:bg-purple-700 transition-colors font-semibold">
                                View Details
                            </button>
                            <button onclick="chatOnWhatsApp('${property.phone}', '${property.title}')"
                                    class="w-full bg-secondary text-white py-3 px-4 rounded-xl hover:bg-green-700 transition-colors font-semibold flex items-center justify-center">
                                <i class="fab fa-whatsapp mr-2 text-lg"></i>Chat on WhatsApp
                            </button>
                        </div>

                        <!-- Community Rating -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                            <span class="text-sm font-medium text-gray-700">Community Rating</span>
                            <div class="flex items-center space-x-4">
                                <button onclick="vote('${property._id}', 'up')"
                                        class="flex items-center space-x-1 text-secondary hover:text-green-700 transition-colors">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span id="upvotes-${property._id}" class="font-semibold">${property.upvotes || 0}</span>
                                </button>
                                <button onclick="vote('${property._id}', 'down')"
                                        class="flex items-center space-x-1 text-red-500 hover:text-red-700 transition-colors">
                                    <i class="fas fa-thumbs-down"></i>
                                    <span id="downvotes-${property._id}" class="font-semibold">${property.downvotes || 0}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </article>
            `).join('');
        }

        // Open property details modal
        function openModal(propertyId) {
            const property = properties.find(p => p._id === propertyId);
            if (!property) return;

            const modal = document.getElementById('propertyModal');
            const modalContent = document.getElementById('modalContent');

            modalContent.innerHTML = `
                <div class="space-y-6">
                    <img src="${property.image_urls && property.image_urls[0] ? property.image_urls[0] : '/placeholder.svg?height=300&width=400'}" alt="${property.title}" class="w-full h-64 object-cover rounded-xl">

                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">${property.title}</h3>
                        <p class="text-gray-600 mb-2 flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-primary"></i>${property.city}
                        </p>
                        <p class="text-3xl font-bold text-primary mb-4">$${property.price}<span class="text-lg text-gray-500">/month</span></p>
                    </div>

                    <div>
                        <h4 class="font-bold text-gray-900 mb-2">Description</h4>
                        <div class="scrollable-description text-gray-600 leading-relaxed">
                            ${property.description}
                        </div>
                    </div>

                    <div>
                        <h4 class="font-bold text-gray-900 mb-3">Bedrooms & Bathrooms</h4>
                        <p class="text-gray-600">${property.bedrooms} ${property.bedrooms === 1 ? 'Bedroom' : 'Bedrooms'}, ${property.bathrooms} ${property.bathrooms === 1 ? 'Bathroom' : 'Bathrooms'}</p>
                    </div>

                    <div>
                        <h4 class="font-bold text-gray-900 mb-3">Furnished Status</h4>
                        <p class="text-gray-600">${property.furnished_status === 'furnished' ? 'Fully Furnished' : property.furnished_status === 'semi' ? 'Semi Furnished' : 'Unfurnished'}</p>
                    </div>

                    <div>
                        <h4 class="font-bold text-gray-900 mb-3">Amenities</h4>
                        <div class="grid grid-cols-2 gap-2">
                            ${(property.amenities || []).map(amenity => `
                                <span class="bg-primary bg-opacity-10 text-primary text-sm px-3 py-2 rounded-lg font-medium">
                                    <i class="fas fa-check mr-2"></i>${amenity}
                                </span>
                            `).join('')}
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                        <span class="font-semibold text-gray-700">Community Rating</span>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-1 text-secondary">
                                <i class="fas fa-thumbs-up"></i>
                                <span class="font-semibold">${property.upvotes || 0}</span>
                            </div>
                            <div class="flex items-center space-x-1 text-red-500">
                                <i class="fas fa-thumbs-down"></i>
                                <span class="font-semibold">${property.downvotes || 0}</span>
                            </div>
                        </div>
                    </div>

                    <button onclick="chatOnWhatsApp('${property.phone}', '${property.title}')"
                            class="w-full bg-secondary text-white py-4 px-6 rounded-xl hover:bg-green-700 transition-colors font-semibold text-lg flex items-center justify-center">
                        <i class="fab fa-whatsapp mr-3 text-xl"></i>Contact Owner
                    </button>
                </div>
            `;

            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // Close modal
        function closeModal() {
            const modal = document.getElementById('propertyModal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Initialize search functionality
        function initializeSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchProperties();
                }
            });
        }

        // Search functionality
        function searchProperties() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            filteredProperties = properties.filter(property =>
                property.title.toLowerCase().includes(searchTerm) ||
                property.location.toLowerCase().includes(searchTerm) ||
                property.description.toLowerCase().includes(searchTerm)
            );
            applyFilters();
        }

        // Filter functionality
        function filterProperties() {
            applyFilters();
        }

        function applyFilters() {
            const priceFilter = document.getElementById('priceFilter').value;
            const locationFilter = document.getElementById('locationFilter').value;

            let filtered = [...filteredProperties];

            // Apply location filter
            if (locationFilter) {
                filtered = filtered.filter(property =>
                    property.location.toLowerCase().includes(locationFilter.toLowerCase())
                );
            }

            // Apply price sorting
            switch (priceFilter) {
                case 'low-to-high':
                    filtered.sort((a, b) => a.price - b.price);
                    break;
                case 'high-to-low':
                    filtered.sort((a, b) => b.price - a.price);
                    break;
                case 'recommended':
                    filtered.sort((a, b) => (b.upvotes - b.downvotes) - (a.upvotes - a.downvotes));
                    break;
            }

            filteredProperties = filtered;
            renderProperties();
        }

        // Voting functionality
        function vote(propertyId, type) {
            const property = properties.find(p => p._id === propertyId);
            if (property) {
                // Send vote to backend first
                sendVoteToBackend(propertyId, type);
            }
        }

        // WhatsApp functionality with property context
        function chatOnWhatsApp(phone, propertyTitle) {
            const message = encodeURIComponent(`Hi! I'm interested in "${propertyTitle}" that I found on HavenHuts. Could you please provide more details?`);
            window.open(`https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${message}`, '_blank');
        }

        // Report functionality
        function reportProperty(propertyId) {
            if (confirm('Are you sure you want to report this property? This will help us maintain quality listings.')) {
                sendReportToBackend(propertyId);
                alert('Property reported successfully. Thank you for helping us maintain quality listings.');
            }
        }

        // Backend integration functions
        async function sendVoteToBackend(propertyId, voteType) {
            try {
                const response = await fetch('/api/vote', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        property_id: propertyId,
                        vote_type: voteType,
                        timestamp: new Date().toISOString()
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to submit vote');
                }

                const result = await response.json();
                if (result.success) {
                    // Update the UI with new vote counts
                    document.getElementById(`upvotes-${propertyId}`).textContent = result.upvotes;
                    document.getElementById(`downvotes-${propertyId}`).textContent = result.downvotes;

                    // Update the property in our local array
                    const property = properties.find(p => p._id === propertyId);
                    if (property) {
                        property.upvotes = result.upvotes;
                        property.downvotes = result.downvotes;
                    }
                }
            } catch (error) {
                console.error('Error submitting vote:', error);
                alert('Failed to submit vote. Please try again.');
            }
        }

        async function sendReportToBackend(propertyId) {
            try {
                const response = await fetch('/api/report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        property_id: propertyId,
                        reason: 'User reported content',
                        timestamp: new Date().toISOString()
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to submit report');
                }
            } catch (error) {
                console.error('Error submitting report:', error);
            }
        }

        // Load properties from backend with SEO data
        async function loadPropertiesFromBackend() {
            try {
                document.getElementById('loadingState').classList.remove('hidden');

                const response = await fetch('/api/properties');
                if (!response.ok) {
                    throw new Error('Failed to fetch properties');
                }

                const data = await response.json();
                if (data.success) {
                    properties = data.data || [];
                    filteredProperties = [...properties];
                    renderProperties();
                } else {
                    throw new Error(data.error || 'Failed to fetch properties');
                }
            } catch (error) {
                console.error('Error loading properties:', error);
                document.getElementById('loadingState').classList.add('hidden');
                document.getElementById('noResultsState').classList.remove('hidden');
            }
        }

        // Close modal when clicking outside
        document.getElementById('propertyModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "HavenHuts",
        "description": "Find your perfect room rental with HavenHuts - premium accommodations and verified properties",
        "url": "https://havenhuts.com",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://havenhuts.com/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
</body>
</html>