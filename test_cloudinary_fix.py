#!/usr/bin/env python3
"""
Test script to verify Cloudinary upload fix
"""

import os
import io
from datetime import datetime, timezone
from dotenv import load_dotenv
import cloudinary
import cloudinary.uploader

def test_cloudinary_upload():
    """Test Cloudinary upload with explicit timestamp"""
    print("🧪 Testing Cloudinary Upload Fix...")
    
    # Load environment variables
    load_dotenv()
    
    # Configure Cloudinary
    cloudinary.config(
        cloud_name=os.getenv('CLOUDINARY_CLOUD_NAME'),
        api_key=os.getenv('CLOUDINARY_API_KEY'),
        api_secret=os.getenv('CLOUDINARY_API_SECRET'),
        secure=True
    )
    
    # Create a simple test image (1x1 pixel PNG)
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    try:
        print("🔍 Testing upload with explicit timestamp...")
        
        # Create file-like object
        file_obj = io.BytesIO(test_image_data)
        
        # Upload with explicit timestamp (like our fixed function)
        result = cloudinary.uploader.upload(
            file_obj,
            folder="test",
            public_id="timestamp_test",
            timestamp=int(datetime.now(timezone.utc).timestamp()),
            resource_type="auto"
        )
        
        if result.get('secure_url'):
            print(f"✅ Upload successful!")
            print(f"   URL: {result['secure_url']}")
            print(f"   Public ID: {result['public_id']}")
            
            # Clean up
            cloudinary.uploader.destroy(result['public_id'])
            print("✅ Test image cleaned up")
            return True
        else:
            print(f"❌ Upload failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Upload failed with error: {e}")
        return False

def test_without_timestamp():
    """Test upload without explicit timestamp (original method)"""
    print("\n🧪 Testing Upload Without Explicit Timestamp...")
    
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    try:
        file_obj = io.BytesIO(test_image_data)
        
        # Upload without explicit timestamp (original method)
        result = cloudinary.uploader.upload(
            file_obj,
            folder="test",
            public_id="no_timestamp_test",
            resource_type="auto"
        )
        
        if result.get('secure_url'):
            print(f"✅ Upload successful!")
            print(f"   URL: {result['secure_url']}")
            
            # Clean up
            cloudinary.uploader.destroy(result['public_id'])
            print("✅ Test image cleaned up")
            return True
        else:
            print(f"❌ Upload failed: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Upload failed with error: {e}")
        return False

def main():
    """Run Cloudinary tests"""
    print("🧪 Cloudinary Upload Fix Test")
    print("=" * 40)
    
    # Test with explicit timestamp (our fix)
    test1_passed = test_cloudinary_upload()
    
    # Test without timestamp (original method)
    test2_passed = test_without_timestamp()
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    print(f"   With explicit timestamp: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Without timestamp: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed:
        print("\n🎉 The Cloudinary fix is working!")
        print("   Property uploads should now work correctly.")
    else:
        print("\n⚠️  The fix didn't resolve the issue.")
        print("   There might be other configuration problems.")
    
    return test1_passed

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
