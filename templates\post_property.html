<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Post Your Property - HavenHuts | List Your Room for Rent</title>
    <meta name="description" content="List your property on HavenHuts and connect with quality tenants. Easy property posting with photo uploads, detailed descriptions, and instant tenant contact.">
    <meta name="keywords" content="post property, list room, rent out room, property listing, HavenHuts">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://havenhuts.com/post-property">

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .drop-zone {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .drop-zone.dragover {
            border-color: #4F46E5;
            background-color: #eff6ff;
        }
        .image-preview {
            position: relative;
            display: inline-block;
        }
        .remove-image {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .mobile-menu {
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .mobile-menu.open {
            transform: translateX(0);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-primary">
                        <i class="fas fa-home mr-2"></i>HavenHuts
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/" class="text-gray-700 hover:text-primary transition-colors font-medium">Home</a>
                    <a href="#" class="text-primary font-semibold">Post Property</a>
                    <a href="#" class="text-gray-700 hover:text-primary transition-colors font-medium">About</a>
                    <a href="#" class="text-gray-700 hover:text-primary transition-colors font-medium">Contact</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobileMenuBtn" class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bars text-gray-700 text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobileMenu" class="mobile-menu fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-50 md:hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-8">
                    <h2 class="text-xl font-bold text-primary">Menu</h2>
                    <button id="closeMobileMenu" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-times text-gray-700 text-xl"></i>
                    </button>
                </div>
                <nav class="space-y-4">
                    <a href="/" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-home mr-3"></i>Home
                    </a>
                    <a href="#" class="block py-3 px-4 bg-primary text-white rounded-lg font-medium">
                        <i class="fas fa-plus mr-3"></i>Post Property
                    </a>
                    <a href="#" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-info-circle mr-3"></i>About
                    </a>
                    <a href="#" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-envelope mr-3"></i>Contact
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="bg-white rounded-2xl shadow-xl p-8 md:p-12">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Post Your Property</h1>
                <p class="text-xl text-gray-600">Share your space with quality tenants and start earning today</p>
            </div>

            <form id="propertyForm" class="space-y-10">
                <!-- Photo Upload Section -->
                <div>
                    <label class="block text-xl font-bold text-gray-900 mb-6">Property Photos *</label>
                    <div id="dropZone" class="drop-zone p-12 text-center rounded-2xl cursor-pointer border-2 border-dashed border-gray-300 hover:border-primary transition-colors">
                        <div class="space-y-6">
                            <i class="fas fa-cloud-upload-alt text-6xl text-gray-400"></i>
                            <div>
                                <p class="text-xl text-gray-600 mb-2">Drag and drop photos here, or</p>
                                <button type="button" onclick="document.getElementById('fileInput').click()"
                                        class="text-primary hover:text-purple-700 font-semibold text-lg underline">
                                    click to browse
                                </button>
                            </div>
                            <p class="text-sm text-gray-500">Support: JPG, PNG, GIF (Max 5MB each, up to 10 photos)</p>
                        </div>
                    </div>
                    <input type="file" id="fileInput" multiple accept="image/*" class="hidden">

                    <!-- Image Previews -->
                    <div id="imagePreviewContainer" class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-6"></div>
                </div>

                <!-- Property Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label for="title" class="block text-lg font-bold text-gray-900 mb-3">Property Title *</label>
                        <input type="text" id="title" name="title" required
                               placeholder="e.g., Spacious 2BHK Flat in Salt Lake, Kolkata"
                               class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                    </div>

                    <div>
                        <label for="price" class="block text-lg font-bold text-gray-900 mb-3">Monthly Rent (₹) *</label>
                        <input type="number" id="price" name="price" required min="0" step="1000"
                               placeholder="25000"
                               class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label for="address" class="block text-lg font-bold text-gray-900 mb-3">Address</label>
                        <input type="text" id="address" name="address"
                               placeholder="e.g., 123 Park Street, Near Victoria Memorial"
                               class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                        <p class="mt-2 text-sm text-gray-500">Optional - Specific address or landmark</p>
                    </div>

                    <div>
                        <label for="city" class="block text-lg font-bold text-gray-900 mb-3">City *</label>
                        <input type="text" id="city" name="city" required
                               placeholder="Kolkata"
                               class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label for="bedrooms" class="block text-lg font-bold text-gray-900 mb-3">Bedrooms</label>
                        <select id="bedrooms" name="bedrooms"
                                class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                            <option value="">Select number of bedrooms (optional)</option>
                            <option value="1">1 Bedroom (Studio)</option>
                            <option value="2">2 Bedrooms</option>
                            <option value="3">3 Bedrooms</option>
                            <option value="4">4+ Bedrooms</option>
                        </select>
                    </div>

                    <div>
                        <label for="bathrooms" class="block text-lg font-bold text-gray-900 mb-3">Bathrooms</label>
                        <select id="bathrooms" name="bathrooms"
                                class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                            <option value="">Select number of bathrooms (optional)</option>
                            <option value="1">1 Bathroom</option>
                            <option value="2">2 Bathrooms</option>
                            <option value="3">3+ Bathrooms</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label for="furnished_status" class="block text-lg font-bold text-gray-900 mb-3">Furnished Status</label>
                        <select id="furnished_status" name="furnished_status"
                                class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                            <option value="">Select furnished status (optional)</option>
                            <option value="furnished">Fully Furnished</option>
                            <option value="semi">Semi Furnished</option>
                            <option value="none">Unfurnished</option>
                        </select>
                    </div>

                    <div>
                        <label for="phone" class="block text-lg font-bold text-gray-900 mb-3">WhatsApp Number *</label>
                        <input type="tel" id="phone" name="phone" required
                               placeholder="+91 98765 43210"
                               class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                        <p class="mt-2 text-sm text-gray-500">This will be your WhatsApp number for property inquiries</p>
                    </div>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-lg font-bold text-gray-900 mb-3">Property Description</label>
                    <textarea id="description" name="description" rows="6"
                              placeholder="Describe your property... Include details about the neighborhood, nearby amenities, transportation, what makes your property special, and any house rules. (Optional but recommended for better responses)"
                              class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent resize-none text-lg"></textarea>
                    <p class="mt-3 text-sm text-gray-500">Optional - Detailed descriptions get more inquiries!</p>
                </div>

                <!-- Amenities -->
                <div>
                    <label class="block text-lg font-bold text-gray-900 mb-6">Amenities & Features</label>
                    <p class="text-sm text-gray-500 mb-4">Select all amenities that apply to your property (optional)</p>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="WiFi" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">WiFi</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Air Conditioning" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Air Conditioning</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Parking" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Parking</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Laundry" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Laundry</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Pet Friendly" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Pet Friendly</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Gym Access" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Gym Access</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Balcony" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Balcony</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="24/7 Security" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">24/7 Security</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Swimming Pool" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Swimming Pool</span>
                        </label>
                    </div>
                </div>



                <!-- Submit Button -->
                <div class="text-center pt-8">
                    <button type="submit" id="submitBtn"
                            class="bg-primary text-white px-12 py-5 rounded-xl text-xl font-bold hover:bg-purple-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed shadow-lg">
                        <span id="submitText">Post Property</span>
                        <i id="submitSpinner" class="fas fa-spinner fa-spin ml-3 hidden"></i>
                    </button>
                    <p class="mt-4 text-gray-500">Your listing will be reviewed and published within 24 hours</p>
                </div>
            </form>
        </div>
    </main>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-2xl p-8 max-w-md mx-4 text-center">
            <div class="mb-6">
                <i class="fas fa-check-circle text-6xl text-secondary mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Property Posted Successfully!</h3>
                <p class="text-gray-600">Your property listing has been submitted and will be reviewed within 24 hours.</p>
            </div>
            <div class="space-y-3">
                <button onclick="closeSuccessModal()"
                        class="w-full bg-primary text-white py-3 px-6 rounded-xl hover:bg-purple-700 transition-colors font-semibold">
                    Post Another Property
                </button>
                <a href="/"
                   class="block w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-xl hover:bg-gray-300 transition-colors text-center font-semibold">
                    View All Properties
                </a>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];

        // Initialize functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeMobileMenu();
            initializeFileUpload();
            initializeForm();
        });

        // Mobile menu functionality
        function initializeMobileMenu() {
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');

            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.add('open');
                document.body.style.overflow = 'hidden';
            });

            closeMobileMenu.addEventListener('click', () => {
                mobileMenu.classList.remove('open');
                document.body.style.overflow = 'auto';
            });

            mobileMenu.addEventListener('click', (e) => {
                if (e.target === mobileMenu) {
                    mobileMenu.classList.remove('open');
                    document.body.style.overflow = 'auto';
                }
            });
        }

        // Initialize file upload functionality
        function initializeFileUpload() {
            const dropZone = document.getElementById('dropZone');
            const fileInput = document.getElementById('fileInput');

            dropZone.addEventListener('dragover', handleDragOver);
            dropZone.addEventListener('dragleave', handleDragLeave);
            dropZone.addEventListener('drop', handleDrop);
            fileInput.addEventListener('change', handleFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            addFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            addFiles(files);
        }

        function addFiles(files) {
            const validFiles = files.filter(file => {
                if (!file.type.startsWith('image/')) {
                    alert(`${file.name} is not an image file.`);
                    return false;
                }
                if (file.size > 5 * 1024 * 1024) {
                    alert(`${file.name} is too large. Maximum size is 5MB.`);
                    return false;
                }
                return true;
            });

            if (selectedFiles.length + validFiles.length > 10) {
                alert('Maximum 10 photos allowed.');
                return;
            }

            selectedFiles = [...selectedFiles, ...validFiles];
            updateImagePreviews();
        }

        function updateImagePreviews() {
            const container = document.getElementById('imagePreviewContainer');
            container.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.createElement('div');
                    preview.className = 'image-preview';
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="Preview ${index + 1}" class="w-full h-32 object-cover rounded-xl shadow-md">
                        <button type="button" onclick="removeImage(${index})" class="remove-image hover:bg-red-600 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    container.appendChild(preview);
                };
                reader.readAsDataURL(file);
            });
        }

        function removeImage(index) {
            selectedFiles.splice(index, 1);
            updateImagePreviews();
        }

        // Initialize form functionality
        function initializeForm() {
            document.getElementById('propertyForm').addEventListener('submit', handleFormSubmit);

            // Phone number formatting
            document.getElementById('phone').addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 6) {
                    value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
                } else if (value.length >= 3) {
                    value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
                }
                e.target.value = value;
            });
        }

        async function handleFormSubmit(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const submitSpinner = document.getElementById('submitSpinner');

            if (!validateForm()) {
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            submitText.textContent = 'Posting Property...';
            submitSpinner.classList.remove('hidden');

            try {
                const formData = new FormData();

                // Add required form fields
                formData.append('title', document.getElementById('title').value);
                formData.append('price', document.getElementById('price').value);
                formData.append('city', document.getElementById('city').value);
                formData.append('phone', document.getElementById('phone').value);

                // Add optional fields (with values if provided, empty if not)
                formData.append('address', document.getElementById('address').value || '');
                formData.append('bedrooms', document.getElementById('bedrooms').value || '');
                formData.append('bathrooms', document.getElementById('bathrooms').value || '');
                formData.append('furnished_status', document.getElementById('furnished_status').value || '');
                formData.append('description', document.getElementById('description').value || '');

                // Add amenities
                const amenities = Array.from(document.querySelectorAll('input[name="amenities"]:checked'))
                    .map(checkbox => checkbox.value);
                formData.append('amenities', JSON.stringify(amenities));

                // Add images
                selectedFiles.forEach((file, index) => {
                    formData.append(`image_${index}`, file);
                });

                // Add metadata for SEO
                formData.append('slug', generateSlug(document.getElementById('title').value));
                formData.append('timestamp', new Date().toISOString());

                // Send to backend
                const response = await fetch('/api/properties', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('Failed to post property');
                }

                const result = await response.json();

                // Show success modal
                document.getElementById('successModal').classList.remove('hidden');
                document.body.style.overflow = 'hidden';

                // Reset form
                document.getElementById('propertyForm').reset();
                selectedFiles = [];
                updateImagePreviews();

            } catch (error) {
                console.error('Error posting property:', error);
                alert('Failed to post property. Please check your connection and try again.');
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                submitText.textContent = 'Post Property';
                submitSpinner.classList.add('hidden');
            }
        }

        function validateForm() {
            const title = document.getElementById('title').value.trim();
            const price = document.getElementById('price').value;
            const city = document.getElementById('city').value.trim();
            const phone = document.getElementById('phone').value.trim();

            // Only validate required fields
            if (!title || !price || !city || !phone) {
                alert('Please fill in all required fields marked with * :\n• Property Title\n• Monthly Rent (₹)\n• City\n• WhatsApp Number\n\nAll other fields are optional but recommended for better responses.');
                return false;
            }

            // Validate title length
            if (title.length < 10) {
                alert('Property title must be at least 10 characters long.');
                return false;
            }

            // Validate price
            if (isNaN(price) || parseFloat(price) <= 0) {
                alert('Please enter a valid monthly rent amount.');
                return false;
            }

            // Validate phone number format (basic)
            const phonePattern = /^[\+]?[0-9\s\-\(\)]{10,15}$/;
            if (!phonePattern.test(phone)) {
                alert('Please enter a valid WhatsApp number (e.g., +91 98765 43210).');
                return false;
            }

            // Optional: Validate description length if provided
            const description = document.getElementById('description').value.trim();
            if (description && description.length < 20) {
                alert('If you provide a description, it should be at least 20 characters long for better results.');
                return false;
            }

            if (selectedFiles.length === 0) {
                alert('Please upload at least one photo of your property.');
                return false;
            }

            return true;
        }

        function generateSlug(title) {
            return title.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
        }

        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    </script>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Post Your Property - HavenHuts",
        "description": "List your property on HavenHuts and connect with quality tenants",
        "url": "https://havenhuts.com/post-property",
        "mainEntity": {
            "@type": "Service",
            "name": "Property Listing Service",
            "provider": {
                "@type": "Organization",
                "name": "HavenHuts"
            }
        }
    }
    </script>
</body>
</html>