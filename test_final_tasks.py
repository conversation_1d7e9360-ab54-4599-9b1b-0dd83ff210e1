#!/usr/bin/env python3
"""
Test for the two specific tasks:
1. SEO Sitemap implementation
2. Restored property form with only 4 required fields
"""

import requests
import json
import xml.etree.ElementTree as ET

def test_seo_sitemap():
    """Test SEO sitemap implementation"""
    print("🧪 Testing SEO Sitemap Implementation...")
    
    try:
        # Test sitemap.xml endpoint
        response = requests.get('http://localhost:5000/sitemap.xml', timeout=10)
        if response.status_code == 200:
            print("   ✅ Sitemap.xml endpoint accessible")
            
            # Check if it's valid XML
            try:
                root = ET.fromstring(response.text)
                print("   ✅ Valid XML format")
                
                # Check for required elements
                urls = root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url')
                if len(urls) > 0:
                    print(f"   ✅ Found {len(urls)} URLs in sitemap")
                    
                    # Check for required pages
                    required_pages = ['/', '/post-property', '/admin/login']
                    found_pages = []
                    
                    for url in urls:
                        loc = url.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                        if loc is not None:
                            for page in required_pages:
                                if page in loc.text:
                                    found_pages.append(page)
                    
                    if len(found_pages) >= 2:
                        print("   ✅ Required pages found in sitemap")
                        return True
                    else:
                        print(f"   ⚠️  Missing some required pages: {found_pages}")
                        return False
                else:
                    print("   ❌ No URLs found in sitemap")
                    return False
                    
            except ET.ParseError:
                print("   ❌ Invalid XML format")
                return False
        else:
            print(f"   ❌ Sitemap endpoint returned status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing sitemap: {e}")
        return False

def test_robots_txt():
    """Test robots.txt implementation"""
    print("\n🧪 Testing robots.txt...")
    
    try:
        response = requests.get('http://localhost:5000/robots.txt', timeout=10)
        if response.status_code == 200:
            content = response.text
            if 'Sitemap:' in content and 'User-agent:' in content:
                print("   ✅ robots.txt properly configured")
                return True
            else:
                print("   ⚠️  robots.txt missing required elements")
                return False
        else:
            print(f"   ❌ robots.txt returned status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing robots.txt: {e}")
        return False

def test_restored_form_fields():
    """Test that all form fields are restored but only 4 are required"""
    print("\n🧪 Testing Restored Property Form Fields...")
    
    try:
        response = requests.get('http://localhost:5000/post-property', timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # Check for all form fields
            all_fields = [
                'name="title"',
                'name="price"', 
                'name="city"',
                'name="phone"',
                'name="address"',
                'name="bedrooms"',
                'name="bathrooms"',
                'name="furnished_status"',
                'name="description"',
                'name="amenities"'
            ]
            
            found_fields = 0
            for field in all_fields:
                if field in content:
                    found_fields += 1
            
            if found_fields >= 8:
                print(f"   ✅ All form fields restored: {found_fields}/{len(all_fields)}")
            else:
                print(f"   ⚠️  Some fields missing: {found_fields}/{len(all_fields)}")
            
            # Check that only 4 fields are required
            required_fields = ['title', 'price', 'city', 'phone']
            required_count = 0
            
            for field in required_fields:
                if f'name="{field}" required' in content:
                    required_count += 1
            
            if required_count == 4:
                print("   ✅ Exactly 4 fields are required")
            else:
                print(f"   ⚠️  Wrong number of required fields: {required_count}/4")
            
            # Check for optional field indicators
            optional_indicators = ['optional', 'Optional', '(optional)']
            has_optional_indicators = any(indicator in content for indicator in optional_indicators)
            
            if has_optional_indicators:
                print("   ✅ Optional field indicators found")
            else:
                print("   ⚠️  No optional field indicators found")
            
            # Check for Kolkata examples
            kolkata_elements = ['Kolkata', 'WhatsApp', '+91']
            kolkata_count = sum(1 for element in kolkata_elements if element in content)
            
            if kolkata_count >= 2:
                print("   ✅ Kolkata-focused examples maintained")
                return True
            else:
                print(f"   ⚠️  Missing Kolkata elements: {kolkata_count}/{len(kolkata_elements)}")
                return False
                
        else:
            print(f"   ❌ Post property form not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing form: {e}")
        return False

def test_minimal_property_creation():
    """Test property creation with only 4 required fields"""
    print("\n🧪 Testing Minimal Property Creation (4 fields only)...")
    
    try:
        # Test with only required fields
        minimal_data = {
            'title': 'Beautiful 2BHK Apartment in Salt Lake, Kolkata',
            'price': '25000',
            'city': 'Kolkata',
            'phone': '+91 98765 43210'
        }
        
        response = requests.post(
            'http://localhost:5000/api/properties',
            data=minimal_data,
            timeout=15
        )
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print("   ✅ Property created with only 4 required fields")
                return True
            else:
                print(f"   ❌ Property creation failed: {result.get('error')}")
                return False
        else:
            print(f"   ❌ Property creation request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Response text: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing minimal creation: {e}")
        return False

def test_comprehensive_property_creation():
    """Test property creation with all fields filled"""
    print("\n🧪 Testing Comprehensive Property Creation (all fields)...")
    
    try:
        # Test with all fields
        comprehensive_data = {
            'title': 'Luxury 3BHK Furnished Apartment in Park Street, Kolkata',
            'price': '45000',
            'city': 'Kolkata',
            'phone': '+91 98765 43210',
            'address': '123 Park Street, Near Victoria Memorial',
            'bedrooms': '3',
            'bathrooms': '2',
            'furnished_status': 'furnished',
            'description': 'Beautiful luxury apartment with all modern amenities in the heart of Kolkata. Close to metro station and shopping centers.',
            'amenities': json.dumps(['WiFi', 'Air Conditioning', 'Parking', '24/7 Security'])
        }
        
        response = requests.post(
            'http://localhost:5000/api/properties',
            data=comprehensive_data,
            timeout=15
        )
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print("   ✅ Property created with all fields")
                return True
            else:
                print(f"   ❌ Comprehensive creation failed: {result.get('error')}")
                return False
        else:
            print(f"   ❌ Comprehensive creation request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing comprehensive creation: {e}")
        return False

def main():
    """Run all tests for the two specific tasks"""
    print("🧪 HavenHuts Final Tasks Test Suite")
    print("=" * 60)
    
    # Test Task 1: SEO Sitemap
    print("📋 TASK 1: SEO SITEMAP IMPLEMENTATION")
    sitemap_test = test_seo_sitemap()
    robots_test = test_robots_txt()
    
    # Test Task 2: Restored Form Fields
    print("\n📋 TASK 2: RESTORED PROPERTY FORM FIELDS")
    form_test = test_restored_form_fields()
    minimal_test = test_minimal_property_creation()
    comprehensive_test = test_comprehensive_property_creation()
    
    print("\n" + "=" * 60)
    print("📊 Final Test Results:")
    print(f"   SEO Sitemap: {'✅ PASSED' if sitemap_test else '❌ FAILED'}")
    print(f"   Robots.txt: {'✅ PASSED' if robots_test else '❌ FAILED'}")
    print(f"   Form Fields Restored: {'✅ PASSED' if form_test else '❌ FAILED'}")
    print(f"   Minimal Creation (4 fields): {'✅ PASSED' if minimal_test else '❌ FAILED'}")
    print(f"   Comprehensive Creation: {'✅ PASSED' if comprehensive_test else '❌ FAILED'}")
    
    task1_success = sitemap_test and robots_test
    task2_success = form_test and minimal_test and comprehensive_test
    
    print("\n" + "=" * 60)
    print("🎯 TASK COMPLETION STATUS:")
    print(f"   Task 1 - SEO Sitemap: {'✅ COMPLETED' if task1_success else '❌ NEEDS WORK'}")
    print(f"   Task 2 - Form Restoration: {'✅ COMPLETED' if task2_success else '❌ NEEDS WORK'}")
    
    if task1_success and task2_success:
        print("\n🎉 BOTH TASKS SUCCESSFULLY COMPLETED!")
        print("\n✅ Your HavenHuts platform now has:")
        print("   • SEO-optimized sitemap.xml with dynamic property URLs")
        print("   • Proper robots.txt for search engine crawling")
        print("   • Complete property form with all original fields")
        print("   • Only 4 mandatory fields (low barrier to entry)")
        print("   • All other fields optional but available")
        print("   • Maintained Kolkata-focused examples")
        print("   • Flexible property creation (minimal or detailed)")
        
        print("\n🚀 Ready for Google Search Console integration!")
        print("🎯 Perfect balance: comprehensive forms + easy posting!")
        
    else:
        print("\n⚠️  Some tasks need attention. Check the details above.")
    
    return task1_success and task2_success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
