#!/usr/bin/env python3
"""Precise verification of the form fields"""

print("🔍 Precise Form Field Analysis")
print("=" * 50)

try:
    with open('templates/post_property.html', 'r') as f:
        content = f.read()
    
    # Count only input/select elements with required attribute
    import re
    
    # Find all input and select elements with required attribute
    required_pattern = r'<(?:input|select)[^>]*\brequired\b[^>]*>'
    required_matches = re.findall(required_pattern, content, re.IGNORECASE)
    
    print(f"Found {len(required_matches)} required form elements:")
    for i, match in enumerate(required_matches, 1):
        # Extract name attribute
        name_match = re.search(r'name="([^"]*)"', match)
        if name_match:
            name = name_match.group(1)
            print(f"   {i}. {name}")
        else:
            print(f"   {i}. (no name attribute)")
    
    # Check for all expected fields
    expected_fields = {
        'title': 'required',
        'price': 'required', 
        'city': 'required',
        'phone': 'required',
        'address': 'optional',
        'bedrooms': 'optional',
        'bathrooms': 'optional',
        'furnished_status': 'optional',
        'description': 'optional',
        'amenities': 'optional'
    }
    
    print(f"\n📋 Field Analysis:")
    all_correct = True
    
    for field, expected_type in expected_fields.items():
        field_pattern = rf'name="{field}"[^>]*'
        field_match = re.search(field_pattern, content)
        
        if field_match:
            has_required = 'required' in field_match.group(0)
            actual_type = 'required' if has_required else 'optional'
            
            if actual_type == expected_type:
                print(f"   ✅ {field}: {actual_type} (correct)")
            else:
                print(f"   ❌ {field}: {actual_type} (expected {expected_type})")
                all_correct = False
        else:
            print(f"   ❌ {field}: missing")
            all_correct = False
    
    # Check for Kolkata focus
    has_kolkata = 'Kolkata' in content
    has_whatsapp = 'WhatsApp' in content
    has_optional_text = 'optional' in content.lower()
    
    print(f"\n🎯 Kolkata Focus:")
    print(f"   ✅ Kolkata examples: {has_kolkata}")
    print(f"   ✅ WhatsApp labeling: {has_whatsapp}")
    print(f"   ✅ Optional indicators: {has_optional_text}")
    
    if len(required_matches) == 4 and all_correct and has_kolkata and has_whatsapp:
        print(f"\n🎉 TASK 2 COMPLETED SUCCESSFULLY!")
        print(f"   • Exactly 4 required fields: ✅")
        print(f"   • All fields present: ✅")
        print(f"   • Correct required/optional setup: ✅")
        print(f"   • Kolkata-focused: ✅")
        success = True
    else:
        print(f"\n⚠️  Task 2 needs attention:")
        if len(required_matches) != 4:
            print(f"   • Required fields count: {len(required_matches)} (should be 4)")
        if not all_correct:
            print(f"   • Field configuration issues detected")
        success = False
        
except Exception as e:
    print(f"❌ Error analyzing form: {e}")
    success = False

print("\n" + "=" * 50)
if success:
    print("✅ FORM RESTORATION TASK COMPLETED!")
else:
    print("❌ FORM RESTORATION TASK NEEDS WORK")
