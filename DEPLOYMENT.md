# HavenHuts CapRover Deployment Guide

This guide will help you deploy HavenHuts to CapRover using GitHub Actions.

## Prerequisites

1. **CapRover Server**: A running CapRover instance
2. **GitHub Repository**: Your HavenHuts code in a GitHub repository
3. **MongoDB Database**: MongoDB Atlas or self-hosted MongoDB
4. **Cloudinary Account**: For image uploads and storage

## Step 1: CapRover Setup

### 1.1 Create App in CapRover
1. Log into your CapRover dashboard
2. Go to "Apps" → "One-Click Apps/Databases"
3. Click "Create New App"
4. Enter app name: `havenhuts` (or your preferred name)
5. Click "Create New App"

### 1.2 Configure App Settings
1. Go to your app's "App Configs" tab
2. Set the following environment variables:

```bash
# Flask Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
FLASK_ENV=production
FLASK_APP=app.py
PORT=5000

# MongoDB Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/
MONGO_DB_NAME=havenhuts

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Admin Panel Configuration
ADMIN_USER=admin
ADMIN_PASS=your-secure-admin-password

# Security Configuration
RATE_LIMIT_STORAGE_URL=memory://
CORS_ORIGINS=https://your-domain.com
```

### 1.3 Enable HTTPS
1. Go to "HTTP Settings" tab
2. Enable "Force HTTPS by redirecting all HTTP traffic to HTTPS"
3. Enable "HTTPS" if you have a custom domain

## Step 2: GitHub Secrets Setup

Add the following secrets to your GitHub repository:

1. Go to your GitHub repository
2. Navigate to "Settings" → "Secrets and variables" → "Actions"
3. Add the following repository secrets:

```bash
CAPROVER_SERVER=your-caprover-server.com
APP_NAME=havenhuts
APP_TOKEN=your-caprover-app-token
```

### How to get CapRover App Token:
1. In CapRover dashboard, go to "Apps" → your app
2. Click on "Deployment" tab
3. Copy the "App Token" from the deployment section

## Step 3: Database Setup

### MongoDB Atlas Setup:
1. Create a MongoDB Atlas account
2. Create a new cluster
3. Create a database user
4. Whitelist your CapRover server IP
5. Get the connection string and add it to CapRover environment variables

### Cloudinary Setup:
1. Create a Cloudinary account
2. Go to Dashboard to get your credentials
3. Add the credentials to CapRover environment variables

## Step 4: Deploy

### Automatic Deployment:
1. Push your code to the `main` branch
2. GitHub Actions will automatically deploy to CapRover
3. Monitor the deployment in GitHub Actions tab

### Manual Deployment:
1. Go to GitHub repository
2. Click "Actions" tab
3. Select "Deploy HavenHuts to CapRover" workflow
4. Click "Run workflow" → "Run workflow"

## Step 5: Post-Deployment

### 5.1 Verify Deployment
1. Check your app URL: `https://havenhuts.your-caprover-server.com`
2. Test the health endpoint: `https://havenhuts.your-caprover-server.com/api/health`
3. Verify all features are working

### 5.2 Configure Domain (Optional)
1. In CapRover, go to your app's "HTTP Settings"
2. Add your custom domain
3. Enable SSL certificate

### 5.3 Monitor Logs
1. Go to CapRover dashboard → your app
2. Click "App Logs" to monitor application logs
3. Check for any errors or issues

## Troubleshooting

### Common Issues:

1. **Deployment Failed**
   - Check GitHub Actions logs
   - Verify CapRover server URL and credentials
   - Ensure app name exists in CapRover

2. **App Not Starting**
   - Check CapRover app logs
   - Verify environment variables
   - Check MongoDB connection

3. **Database Connection Issues**
   - Verify MongoDB URI
   - Check IP whitelist in MongoDB Atlas
   - Test connection from CapRover server

4. **Image Upload Issues**
   - Verify Cloudinary credentials
   - Check Cloudinary quota and limits

### Health Check Endpoints:
- **Health**: `/api/health` - Basic health check
- **Properties API**: `/api/properties` - Test database connection

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to Git
2. **HTTPS**: Always use HTTPS in production
3. **Admin Password**: Use a strong admin password
4. **Database**: Use MongoDB Atlas with proper authentication
5. **Rate Limiting**: Monitor and adjust rate limits as needed

## Scaling

### Horizontal Scaling:
1. In CapRover, go to your app
2. Increase "Instance Count" in App Configs
3. CapRover will automatically load balance

### Vertical Scaling:
1. Increase server resources in your hosting provider
2. Adjust worker count in Dockerfile if needed

## Backup Strategy

1. **Database**: Set up automated backups in MongoDB Atlas
2. **Images**: Cloudinary automatically handles image storage
3. **Code**: GitHub repository serves as code backup

## Monitoring

1. **CapRover Logs**: Monitor application logs
2. **MongoDB Atlas**: Monitor database performance
3. **Cloudinary**: Monitor image storage usage
4. **Health Checks**: Set up external monitoring for `/api/health`

## Support

For deployment issues:
1. Check CapRover documentation
2. Review GitHub Actions logs
3. Monitor application logs in CapRover
4. Test individual components (database, image upload, etc.)

---

**Your HavenHuts platform is now ready for production deployment!** 🚀
