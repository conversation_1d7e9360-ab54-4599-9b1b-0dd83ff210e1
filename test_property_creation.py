#!/usr/bin/env python3
"""
Test script to verify property creation without images
"""

import requests
import json

def test_property_creation():
    """Test creating a property without images"""
    print("🧪 Testing Property Creation (without images)...")
    
    # Property data
    property_data = {
        'title': 'Test Property - No Images',
        'description': 'This is a test property created to verify that the backend works correctly without images. It has a long description to meet the minimum character requirement for property descriptions.',
        'address': '123 Test Street',
        'city': 'Test City',
        'price': '1000',
        'bedrooms': '2',
        'bathrooms': '1',
        'furnished_status': 'furnished',
        'phone': '+1234567890'
    }
    
    try:
        # Send POST request to create property
        response = requests.post(
            'http://localhost:5000/api/properties',
            data=property_data,
            timeout=10
        )
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print("✅ Property created successfully!")
                print(f"   Property ID: {result.get('property_id')}")
                print(f"   Message: {result.get('message')}")
                return True, result.get('property_id')
            else:
                print(f"❌ Property creation failed: {result.get('error')}")
                return False, None
        else:
            print(f"❌ HTTP Error {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Response: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False, None

def test_get_properties():
    """Test getting properties to see if our created property appears"""
    print("\n🧪 Testing Get Properties...")
    
    try:
        response = requests.get('http://localhost:5000/api/properties', timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                properties = result.get('data', [])
                print(f"✅ Found {len(properties)} properties")
                
                # Look for our test property
                test_properties = [p for p in properties if 'Test Property' in p.get('title', '')]
                if test_properties:
                    print(f"✅ Found {len(test_properties)} test properties")
                    for prop in test_properties:
                        print(f"   - {prop['title']} (Status: {prop.get('status', 'unknown')})")
                else:
                    print("ℹ️  No test properties found (they might be pending approval)")
                
                return True
            else:
                print(f"❌ Get properties failed: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_admin_dashboard():
    """Test admin dashboard to see pending properties"""
    print("\n🧪 Testing Admin Dashboard...")
    
    try:
        # Use basic auth (admin:admin123)
        response = requests.get(
            'http://localhost:5000/admin',
            auth=('admin', 'admin123'),
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('stats', {})
                print("✅ Admin dashboard accessible")
                print(f"   Pending: {stats.get('pending', 0)}")
                print(f"   Approved: {stats.get('approved', 0)}")
                print(f"   Total: {stats.get('total', 0)}")
                return True
            else:
                print(f"❌ Admin dashboard failed: {result.get('error')}")
                return False
        elif response.status_code == 401:
            print("❌ Admin authentication failed")
            print("   Check ADMIN_USER and ADMIN_PASS in your .env file")
            return False
        else:
            print(f"❌ HTTP Error {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 HavenHuts Property Creation Test")
    print("=" * 50)
    
    # Test property creation
    success, property_id = test_property_creation()
    
    # Test getting properties
    get_success = test_get_properties()
    
    # Test admin dashboard
    admin_success = test_admin_dashboard()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Property Creation: {'✅ PASSED' if success else '❌ FAILED'}")
    print(f"   Get Properties: {'✅ PASSED' if get_success else '❌ FAILED'}")
    print(f"   Admin Dashboard: {'✅ PASSED' if admin_success else '❌ FAILED'}")
    
    if success:
        print("\n🎉 Property creation is working!")
        print("   You can now create properties without images.")
        print("   The Cloudinary issue is bypassed for now.")
        print("\nNext steps:")
        print("   1. Go to http://localhost:5000/admin/panel")
        print("   2. Login with your admin credentials")
        print("   3. Approve the pending property")
        print("   4. Check http://localhost:5000/ to see it appear")
    else:
        print("\n⚠️  Property creation is still not working.")
        print("   Check the server logs for more details.")
    
    return success and get_success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
