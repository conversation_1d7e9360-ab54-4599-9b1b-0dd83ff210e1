#!/usr/bin/env python3
"""
Simple verification for the two completed tasks
"""

import requests
import xml.etree.ElementTree as ET

def verify_sitemap():
    """Verify sitemap is working"""
    try:
        response = requests.get('http://localhost:5000/sitemap.xml', timeout=5)
        if response.status_code == 200:
            # Check if it's valid XML
            root = ET.fromstring(response.text)
            urls = root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url')
            return len(urls) > 0
        return False
    except:
        return False

def verify_form():
    """Verify form has all fields but only 4 required"""
    try:
        response = requests.get('http://localhost:5000/post-property', timeout=5)
        if response.status_code == 200:
            content = response.text

            # Check for all form fields
            required_fields = ['name="title"', 'name="price"', 'name="city"', 'name="phone"']
            optional_fields = ['name="address"', 'name="bedrooms"', 'name="bathrooms"',
                             'name="furnished_status"', 'name="description"', 'name="amenities"']

            # Check all fields exist
            has_required = all(field in content for field in required_fields)
            has_optional = all(field in content for field in optional_fields)

            # Check exactly 4 required attributes
            has_required_only = content.count(' required') == 4

            # Check for Kolkata focus
            has_kolkata = 'Kolkata' in content and 'WhatsApp' in content

            # Check for optional indicators
            has_optional_indicators = 'optional' in content.lower()

            return has_required and has_optional and has_required_only and has_kolkata and has_optional_indicators
        return False
    except:
        return False

def main():
    print("🧪 Quick Verification of Completed Tasks")
    print("=" * 50)

    sitemap_ok = verify_sitemap()
    form_ok = verify_form()

    print(f"✅ Task 1 - SEO Sitemap: {'WORKING' if sitemap_ok else 'FAILED'}")
    print(f"✅ Task 2 - Form Restoration: {'WORKING' if form_ok else 'FAILED'}")

    if sitemap_ok and form_ok:
        print("\n🎉 BOTH TASKS COMPLETED SUCCESSFULLY!")
        print("\n📋 What's been implemented:")
        print("   • SEO sitemap.xml with proper XML structure")
        print("   • Dynamic sitemap generation with property URLs")
        print("   • robots.txt for search engine optimization")
        print("   • Complete property form with all original fields")
        print("   • Only 4 fields required: title, city, price, WhatsApp")
        print("   • All other fields optional but available")
        print("   • Maintained Kolkata-focused examples")
        print("   • Flexible property creation workflow")

        print("\n🌐 Access your platform:")
        print("   • Homepage: http://localhost:5000")
        print("   • Post Property: http://localhost:5000/post-property")
        print("   • Sitemap: http://localhost:5000/sitemap.xml")
        print("   • Robots: http://localhost:5000/robots.txt")

        return True
    else:
        print("\n⚠️  Some issues detected.")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
