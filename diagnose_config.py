#!/usr/bin/env python3
"""
Configuration Diagnostic Script for HavenHuts
Checks MongoDB and Cloudinary connections
"""

import os
import sys
from datetime import datetime, timezone
from dotenv import load_dotenv

def check_environment_variables():
    """Check if all required environment variables are set"""
    print("🔍 Checking Environment Variables...")
    
    # Load environment variables
    load_dotenv()
    
    required_vars = {
        'MONGO_URI': os.getenv('MONGO_URI'),
        'MONGO_DB_NAME': os.getenv('MONGO_DB_NAME'),
        'CLOUDINARY_CLOUD_NAME': os.getenv('CLOUDINARY_CLOUD_NAME'),
        'CLOUDINARY_API_KEY': os.getenv('CLOUDINARY_API_KEY'),
        'CLOUDINARY_API_SECRET': os.getenv('CLOUDINARY_API_SECRET'),
    }
    
    missing_vars = []
    for var_name, var_value in required_vars.items():
        if var_value:
            # Mask sensitive values
            if 'SECRET' in var_name or 'URI' in var_name:
                display_value = f"{var_value[:10]}...{var_value[-4:]}" if len(var_value) > 14 else "***"
            else:
                display_value = var_value
            print(f"✅ {var_name}: {display_value}")
        else:
            print(f"❌ {var_name}: NOT SET")
            missing_vars.append(var_name)
    
    return len(missing_vars) == 0, missing_vars

def test_mongodb_connection():
    """Test MongoDB connection"""
    print("\n🔍 Testing MongoDB Connection...")
    
    try:
        from pymongo import MongoClient
        
        mongo_uri = os.getenv('MONGO_URI')
        db_name = os.getenv('MONGO_DB_NAME', 'eznest')
        
        if not mongo_uri:
            print("❌ MONGO_URI not configured")
            return False
        
        # Test connection
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        client.admin.command('ping')
        print("✅ MongoDB connection successful")
        
        # Test database access
        db = client[db_name]
        properties_collection = db.properties
        
        # Try to count documents
        count = properties_collection.count_documents({})
        print(f"✅ Database '{db_name}' accessible")
        print(f"✅ Properties collection has {count} documents")
        
        # Test write permission
        test_doc = {
            'test': True,
            'timestamp': datetime.now(timezone.utc),
            'purpose': 'connection_test'
        }
        
        result = properties_collection.insert_one(test_doc)
        print(f"✅ Write permission confirmed (test doc: {result.inserted_id})")
        
        # Clean up test document
        properties_collection.delete_one({'_id': result.inserted_id})
        print("✅ Test document cleaned up")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        return False

def test_cloudinary_connection():
    """Test Cloudinary connection and configuration"""
    print("\n🔍 Testing Cloudinary Connection...")
    
    try:
        import cloudinary
        import cloudinary.api
        import cloudinary.uploader
        
        cloud_name = os.getenv('CLOUDINARY_CLOUD_NAME')
        api_key = os.getenv('CLOUDINARY_API_KEY')
        api_secret = os.getenv('CLOUDINARY_API_SECRET')
        
        if not all([cloud_name, api_key, api_secret]):
            print("❌ Cloudinary credentials not fully configured")
            return False
        
        # Configure Cloudinary
        cloudinary.config(
            cloud_name=cloud_name,
            api_key=api_key,
            api_secret=api_secret,
            secure=True
        )
        
        print(f"✅ Cloudinary configured for cloud: {cloud_name}")
        
        # Test API connection
        result = cloudinary.api.ping()
        if result.get('status') == 'ok':
            print("✅ Cloudinary API connection successful")
        else:
            print(f"❌ Cloudinary API ping failed: {result}")
            return False
        
        # Test upload capability with a small test image
        print("🔍 Testing image upload capability...")
        
        # Create a simple test image data (1x1 pixel PNG)
        test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        
        upload_result = cloudinary.uploader.upload(
            test_image_data,
            folder="test",
            public_id="connection_test",
            resource_type="image"
        )
        
        if upload_result.get('secure_url'):
            print(f"✅ Test image uploaded successfully")
            print(f"   URL: {upload_result['secure_url']}")
            
            # Clean up test image
            cloudinary.uploader.destroy(upload_result['public_id'])
            print("✅ Test image cleaned up")
            return True
        else:
            print(f"❌ Image upload failed: {upload_result}")
            return False
            
    except Exception as e:
        print(f"❌ Cloudinary connection failed: {e}")
        return False

def check_system_time():
    """Check if system time is correct"""
    print("\n🔍 Checking System Time...")
    
    try:
        import requests
        
        # Get current system time
        local_time = datetime.now(timezone.utc)
        print(f"✅ Local UTC time: {local_time.isoformat()}")
        
        # Get time from a reliable source
        try:
            response = requests.get('http://worldtimeapi.org/api/timezone/UTC', timeout=5)
            if response.status_code == 200:
                data = response.json()
                server_time = datetime.fromisoformat(data['utc_datetime'].replace('Z', '+00:00'))
                
                time_diff = abs((local_time - server_time).total_seconds())
                print(f"✅ Server UTC time: {server_time.isoformat()}")
                print(f"✅ Time difference: {time_diff:.1f} seconds")
                
                if time_diff > 300:  # 5 minutes
                    print("⚠️  WARNING: System time differs by more than 5 minutes!")
                    print("   This could cause Cloudinary authentication issues.")
                    return False
                else:
                    print("✅ System time is accurate")
                    return True
            else:
                print("⚠️  Could not verify system time (network issue)")
                return True
        except:
            print("⚠️  Could not verify system time (network issue)")
            return True
            
    except Exception as e:
        print(f"❌ Time check failed: {e}")
        return True  # Don't fail the whole test for this

def main():
    """Run all diagnostic tests"""
    print("🧪 HavenHuts Configuration Diagnostics")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", check_environment_variables),
        ("System Time", check_system_time),
        ("MongoDB Connection", test_mongodb_connection),
        ("Cloudinary Connection", test_cloudinary_connection),
    ]
    
    passed = 0
    total = len(tests)
    failed_tests = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if isinstance(result, tuple):
                success, details = result
                if not success:
                    failed_tests.append((test_name, details))
            else:
                success = result
                if not success:
                    failed_tests.append((test_name, "Test failed"))
            
            if success:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            failed_tests.append((test_name, str(e)))
    
    print("\n" + "=" * 50)
    print(f"📊 Diagnostic Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your configuration should work correctly.")
    else:
        print("⚠️  Some tests failed. Issues found:")
        for test_name, details in failed_tests:
            print(f"   - {test_name}: {details}")
        
        print("\n💡 Recommended fixes:")
        if any("Environment Variables" in test[0] for test in failed_tests):
            print("   1. Create/update your .env file with correct credentials")
            print("   2. Copy .env.example to .env and fill in your values")
        
        if any("Cloudinary" in test[0] for test in failed_tests):
            print("   3. Check your Cloudinary credentials at https://cloudinary.com/console")
            print("   4. Make sure your Cloudinary account is active")
        
        if any("MongoDB" in test[0] for test in failed_tests):
            print("   5. Check your MongoDB Atlas connection string")
            print("   6. Ensure your IP is whitelisted in MongoDB Atlas")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
