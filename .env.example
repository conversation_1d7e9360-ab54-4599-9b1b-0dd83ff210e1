# Flask Configuration
SECRET_KEY=your-secret-key-here-change-in-production
FLASK_ENV=development
PORT=5000

# MongoDB Configuration (MongoDB Atlas)
MONGO_URI=mongodb+srv://username:<EMAIL>/
MONGO_DB_NAME=eznest

# Cloudinary Configuration (Get from https://cloudinary.com)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Admin Authentication
ADMIN_USER=admin
ADMIN_PASS=your-secure-admin-password

# CORS Configuration (comma-separated origins)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,https://yourdomain.com

# Optional: Redis Configuration for Caching
# REDIS_URL=redis://localhost:6379/0
