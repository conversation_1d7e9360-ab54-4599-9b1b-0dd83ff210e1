// MongoDB initialization script for HavenHuts
// This script creates the database and initial collections with indexes

// Switch to the havenhuts database
db = db.getSiblingDB('havenhuts');

// Create the properties collection
db.createCollection('properties');

// Create indexes for better performance
db.properties.createIndex({ "title": "text", "description": "text" });
db.properties.createIndex({ "city": 1 });
db.properties.createIndex({ "price": 1 });
db.properties.createIndex({ "bedrooms": 1 });
db.properties.createIndex({ "bathrooms": 1 });
db.properties.createIndex({ "furnished_status": 1 });
db.properties.createIndex({ "status": 1 });
db.properties.createIndex({ "created_at": -1 });

// Insert sample data for testing (optional)
db.properties.insertMany([
    {
        title: "Cozy Studio Apartment Downtown",
        description: "Beautiful studio apartment in the heart of downtown. Perfect for young professionals with easy access to public transportation, restaurants, and entertainment. This modern unit features high ceilings, large windows with city views, and premium finishes throughout.",
        address: "123 Main Street",
        city: "San Francisco",
        price: 1200,
        bedrooms: 1,
        bathrooms: 1,
        furnished_status: "furnished",
        image_urls: [
            "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop",
            "https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop"
        ],
        phone: "+14155551234",
        amenities: ["WiFi", "Air Conditioning", "Furnished", "Laundry", "Gym Access"],
        upvotes: 24,
        downvotes: 3,
        reports: [],
        status: "approved",
        created_at: new Date(),
        updated_at: new Date(),
        slug: "cozy-studio-apartment-downtown-sf"
    },
    {
        title: "Spacious 2BR Near Campus",
        description: "Perfect for students! This spacious 2-bedroom apartment is just a 5-minute walk from the university campus. Recently renovated with modern appliances, high-speed internet, and study-friendly environment. The unit includes all utilities and parking space.",
        address: "456 University Ave",
        city: "Seattle",
        price: 800,
        bedrooms: 2,
        bathrooms: 1,
        furnished_status: "semi",
        image_urls: [
            "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop",
            "https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=800&h=600&fit=crop"
        ],
        phone: "+12065551234",
        amenities: ["WiFi", "Parking", "Study Room", "All Utilities Included"],
        upvotes: 18,
        downvotes: 1,
        reports: [],
        status: "approved",
        created_at: new Date(),
        updated_at: new Date(),
        slug: "spacious-2br-near-campus-seattle"
    },
    {
        title: "Modern Loft in Suburbs",
        description: "A stunning modern loft-style apartment in a quiet suburban neighborhood. Features high ceilings, exposed brick walls, large windows, and a private balcony with garden views. The unit comes with premium appliances and in-unit laundry.",
        address: "789 Oak Street",
        city: "Austin",
        price: 950,
        bedrooms: 1,
        bathrooms: 1,
        furnished_status: "none",
        image_urls: [
            "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop",
            "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop"
        ],
        phone: "+15125551234",
        amenities: ["Balcony", "Parking", "Pet Friendly", "Garden", "In-unit Laundry"],
        upvotes: 15,
        downvotes: 2,
        reports: [],
        status: "approved",
        created_at: new Date(),
        updated_at: new Date(),
        slug: "modern-loft-suburbs-austin"
    }
]);

print("HavenHuts database initialized successfully!");
print("Created collections: properties");
print("Created indexes for optimal performance");
print("Inserted sample data for testing");
