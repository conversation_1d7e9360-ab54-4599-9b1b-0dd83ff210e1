name: Deploy HavenHuts to CapRover

on:
  push:
    branches: [main]
  workflow_dispatch:  # Allow manual deployment

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    env:
      NODE_TLS_REJECT_UNAUTHORIZED: '0'

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install Dependencies
        run: |
          pip install --upgrade pip
          pip install -r requirements.txt

      - name: Run Tests (Optional)
        run: |
          # Add your test commands here if you have tests
          python -c "import app; print('✅ App imports successfully')"
          echo " Basic validation passed"

      # Create deployment tarball with all necessary files
      - name: Create deployment bundle
        uses: a7ul/tar-action@v1.1.0
        with:
          command: c
          cwd: "./"
          files: |
            app.py
            wsgi.py
            requirements.txt
            Dockerfile
            captain-definition
            .env.example
            templates/
            static/
            sitemap.xml
          outPath: deploy.tar

      # Deploy to CapRover
      - name: Deploy to CapRover
        uses: caprover/deploy-from-github@v1.1.2
        with:
          server: ${{ secrets.CAPROVER_SERVER }}
          app: ${{ secrets.APP_NAME }}
          token: ${{ secrets.APP_TOKEN }}

      - name: Deployment Success Notification
        if: success()
        run: |
          echo " HavenHuts deployed successfully to CapRover!"
          echo " Your app should be available at: https://${{ secrets.APP_NAME }}.${{ secrets.CAPROVER_SERVER }}"

      - name: Deployment Failure Notification
        if: failure()
        run: |
          echo " Deployment failed. Please check the logs above."
          echo "🔧 Common issues:"
          echo "   • Check CapRover server URL and credentials"
          echo "   • Verify app name exists in CapRover"
          echo "   • Ensure all required secrets are set"
