# HavenHuts v0 Backend - Production-Ready Room Rental Platform

A complete Flask-based backend for a room rental platform with MongoDB, Cloudinary integration, admin panel, and comprehensive API endpoints.

## 🚀 Features

### Core Functionality
- **Property Listings**: Create, read, update, and delete property listings
- **Image Management**: Cloudinary integration for optimized image storage and delivery
- **Search & Filtering**: Advanced search with text indexing and multiple filters
- **Community Features**: Voting system and property reporting
- **Admin Panel**: Complete admin interface for property moderation

### Security & Performance
- **Rate Limiting**: Flask-Limiter for API protection
- **CORS Configuration**: Configurable cross-origin resource sharing
- **Input Validation**: Comprehensive data validation and sanitization
- **MongoDB Indexing**: Optimized database queries with proper indexing
- **Error Handling**: Robust error handling with logging

### API Endpoints
- `GET /` - Serve index.html
- `GET /post-property` - Serve post property page
- `GET /api/properties` - Get properties with filtering and pagination
- `POST /api/properties` - Create new property listing
- `POST /api/vote` - Vote on properties
- `POST /api/report` - Report properties
- `GET /admin/*` - Admin panel endpoints
- `GET /health` - Health check endpoint

## 📋 Prerequisites

- Python 3.8+
- MongoDB 4.4+
- Cloudinary account
- Git

## 🛠️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd havenhuts
```

### 2. Create Virtual Environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Environment Configuration
```bash
cp .env.example .env
```

Edit `.env` file with your configuration:

```env
# Flask Configuration
SECRET_KEY=your-super-secret-key-here
FLASK_ENV=development
PORT=5000

# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017/
MONGO_DB_NAME=havenhuts

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Admin Authentication
ADMIN_USER=admin
ADMIN_PASS=your-secure-password

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
```

### 5. Database Setup

Ensure MongoDB is running locally or provide a remote MongoDB URI in your `.env` file.

The application will automatically create indexes on startup:
- Text index on `title` and `description`
- Single-field indexes on `city`, `price`, `bedrooms`, `furnished_status`, `status`, `created_at`

## 🚀 Running the Application

### Development Mode
```bash
python app.py
```

### Production Mode
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

The application will be available at `http://localhost:5000`

## 📊 MongoDB Schema

### Properties Collection
```json
{
  "_id": "ObjectId",
  "title": "string",
  "description": "string",
  "address": "string", 
  "city": "string",
  "price": "number",
  "bedrooms": "number",
  "bathrooms": "number",
  "furnished_status": "furnished|semi|none",
  "image_urls": ["string"],
  "phone": "string",
  "amenities": ["string"],
  "upvotes": "number",
  "downvotes": "number", 
  "reports": [
    {
      "reason": "string",
      "timestamp": "ISODate",
      "ip_address": "string"
    }
  ],
  "status": "pending|approved|rejected",
  "created_at": "ISODate",
  "updated_at": "ISODate",
  "slug": "string"
}
```

## 🔧 API Usage Examples

### Get Properties with Filtering
```bash
curl "http://localhost:5000/api/properties?city=San Francisco&minPrice=1000&maxPrice=2000&page=1&per_page=10"
```

### Create Property
```bash
curl -X POST http://localhost:5000/api/properties \
  -F "title=Cozy Studio Downtown" \
  -F "price=1200" \
  -F "city=San Francisco" \
  -F "address=123 Main St" \
  -F "bedrooms=1" \
  -F "bathrooms=1" \
  -F "description=Beautiful studio apartment..." \
  -F "phone=+1234567890" \
  -F "image_0=@photo1.jpg" \
  -F "image_1=@photo2.jpg"
```

### Vote on Property
```bash
curl -X POST http://localhost:5000/api/vote \
  -H "Content-Type: application/json" \
  -d '{"property_id": "64f8a1b2c3d4e5f6a7b8c9d0", "vote_type": "up"}'
```

## 👨‍💼 Admin Panel

Access the admin panel with HTTP Basic Authentication:
- URL: `http://localhost:5000/admin`
- Username: Set in `ADMIN_USER` environment variable
- Password: Set in `ADMIN_PASS` environment variable

### Admin Endpoints
- `GET /admin` - Dashboard with statistics
- `GET /admin/properties/pending` - List pending properties
- `POST /admin/properties/{id}/approve` - Approve property
- `POST /admin/properties/{id}/reject` - Reject property
- `GET /admin/properties/reported` - List reported properties
- `POST /admin/properties/{id}/dismiss-reports` - Dismiss reports
- `DELETE /admin/properties/{id}/delete` - Delete property

## 🔒 Security Features

- **Rate Limiting**: 5 requests per minute for write operations
- **Input Validation**: Comprehensive validation for all inputs
- **File Upload Security**: File type and size validation
- **Admin Authentication**: HTTP Basic Auth for admin endpoints
- **CORS Protection**: Configurable allowed origins
- **Error Handling**: No sensitive information in error responses

## 📈 Performance Optimizations

- **MongoDB Indexing**: Optimized queries with proper indexes
- **Image Optimization**: Cloudinary automatic optimization
- **Pagination**: Efficient pagination for large datasets
- **Connection Pooling**: MongoDB connection pooling
- **Caching Ready**: Structure ready for Redis caching implementation

## 🚀 Deployment

### Environment Variables for Production
```env
FLASK_ENV=production
SECRET_KEY=your-production-secret-key
MONGO_URI=mongodb+srv://user:<EMAIL>/havenhuts
ALLOWED_ORIGINS=https://yourdomain.com
```

### Using Gunicorn
```bash
gunicorn -w 4 -b 0.0.0.0:$PORT app:app
```

### Docker Deployment
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

## 🧪 Testing

### Health Check
```bash
curl http://localhost:5000/health
```

### Test Property Creation
Use the frontend form at `http://localhost:5000/post-property` or use the curl example above.

## 📝 Logging

The application logs important events:
- Property creation, approval, rejection
- Vote submissions
- Report submissions
- Error conditions
- Admin actions

Logs are output to console and can be redirected to files in production.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, please check the logs first:
- Application logs for runtime errors
- MongoDB logs for database issues
- Cloudinary dashboard for image upload issues

Common issues:
- **MongoDB connection**: Check MONGO_URI and ensure MongoDB is running
- **Cloudinary uploads**: Verify API credentials and network connectivity
- **Rate limiting**: Check if you're hitting rate limits (5 req/min for writes)
