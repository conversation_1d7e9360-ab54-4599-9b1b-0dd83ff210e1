#!/usr/bin/env python3
"""
Test the fixed admin login system
"""

import requests
import json

def test_admin_login_form():
    """Test the new session-based admin login"""
    print("🧪 Testing Session-Based Admin Login...")
    
    # Test login with correct credentials
    login_data = {
        'username': 'admin',
        'password': 'Interpreter@1435'
    }
    
    try:
        # Create a session to maintain cookies
        session = requests.Session()
        
        # Test login
        response = session.post(
            'http://localhost:5000/admin/login',
            json=login_data,
            timeout=10
        )
        
        print(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Login successful!")
                print(f"   Redirect URL: {result.get('redirect')}")
                
                # Test accessing admin panel with session
                panel_response = session.get('http://localhost:5000/admin/panel', timeout=10)
                
                if panel_response.status_code == 200:
                    print("✅ Admin panel accessible with session!")
                    return True, session
                else:
                    print(f"❌ Admin panel not accessible: {panel_response.status_code}")
                    return False, None
            else:
                print(f"❌ Login failed: {result.get('error')}")
                return False, None
        else:
            print(f"❌ Login request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Response: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ Login test failed: {e}")
        return False, None

def test_admin_api_with_session(session):
    """Test admin API endpoints with session"""
    print("\n🧪 Testing Admin API with Session...")
    
    try:
        # Test admin dashboard
        response = session.get('http://localhost:5000/admin', timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('stats', {})
                print("✅ Admin dashboard API working!")
                print(f"   Stats: {stats}")
                return True
            else:
                print(f"❌ Admin dashboard error: {result.get('error')}")
                return False
        else:
            print(f"❌ Admin dashboard failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Admin API test failed: {e}")
        return False

def test_admin_panel_redirect():
    """Test that admin panel redirects when not logged in"""
    print("\n🧪 Testing Admin Panel Redirect (No Session)...")
    
    try:
        # Test without session
        response = requests.get('http://localhost:5000/admin/panel', timeout=10, allow_redirects=False)
        
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            if '/admin/login' in location:
                print("✅ Admin panel correctly redirects to login when not authenticated")
                return True
            else:
                print(f"❌ Admin panel redirects to wrong location: {location}")
                return False
        else:
            print(f"❌ Admin panel should redirect but returned: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Redirect test failed: {e}")
        return False

def test_wrong_credentials():
    """Test login with wrong credentials"""
    print("\n🧪 Testing Wrong Credentials...")
    
    login_data = {
        'username': 'admin',
        'password': 'wrongpassword'
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/admin/login',
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 401:
            result = response.json()
            if not result.get('success'):
                print("✅ Wrong credentials correctly rejected")
                print(f"   Error message: {result.get('error')}")
                return True
            else:
                print("❌ Wrong credentials were accepted!")
                return False
        else:
            print(f"❌ Expected 401 but got: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Wrong credentials test failed: {e}")
        return False

def main():
    """Run all admin login tests"""
    print("🧪 Admin Login Fix Test")
    print("=" * 50)
    
    # Test login form
    login_success, session = test_admin_login_form()
    
    # Test admin API with session
    api_success = False
    if login_success and session:
        api_success = test_admin_api_with_session(session)
    
    # Test redirect when not logged in
    redirect_success = test_admin_panel_redirect()
    
    # Test wrong credentials
    wrong_creds_success = test_wrong_credentials()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Session Login: {'✅ PASSED' if login_success else '❌ FAILED'}")
    print(f"   Admin API: {'✅ PASSED' if api_success else '❌ FAILED'}")
    print(f"   Redirect: {'✅ PASSED' if redirect_success else '❌ FAILED'}")
    print(f"   Wrong Credentials: {'✅ PASSED' if wrong_creds_success else '❌ FAILED'}")
    
    all_passed = login_success and api_success and redirect_success and wrong_creds_success
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED! Admin login is now working perfectly!")
        print("\n✅ What's Fixed:")
        print("   - Session-based authentication working")
        print("   - Login form properly authenticates users")
        print("   - Admin panel accessible after login")
        print("   - Admin API endpoints working with session")
        print("   - Proper redirect when not authenticated")
        print("   - Wrong credentials properly rejected")
        
        print("\n🚀 You can now:")
        print("   1. Go to http://localhost:5000/admin/login")
        print("   2. Login with: admin / Interpreter@1435")
        print("   3. Access the admin panel successfully")
        print("   4. Manage properties through the admin interface")
        
    else:
        print("\n⚠️  Some tests failed. Check the issues above.")
    
    return all_passed

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
