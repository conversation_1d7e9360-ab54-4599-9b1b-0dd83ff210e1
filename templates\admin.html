<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - HavenHuts</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-primary">
                        <i class="fas fa-shield-alt mr-2"></i>HavenHuts Admin
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-gray-700 hover:text-primary transition-colors">
                        <i class="fas fa-home mr-1"></i>Back to Site
                    </a>
                    <button onclick="logout()" class="text-red-600 hover:text-red-700 transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>Logout
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending</p>
                        <p id="pendingCount" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-600">
                        <i class="fas fa-flag text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Reported</p>
                        <p id="reportedCount" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-check text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Approved</p>
                        <p id="approvedCount" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-home text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total</p>
                        <p id="totalCount" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="bg-white rounded-lg shadow">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button onclick="showTab('pending')" id="pendingTab" 
                            class="tab-button py-4 px-1 border-b-2 font-medium text-sm border-primary text-primary">
                        Pending Properties
                    </button>
                    <button onclick="showTab('reported')" id="reportedTab"
                            class="tab-button py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        Reported Properties
                    </button>
                </nav>
            </div>

            <!-- Pending Properties Tab -->
            <div id="pendingContent" class="tab-content p-6">
                <div id="pendingProperties" class="space-y-4">
                    <!-- Properties will be loaded here -->
                </div>
            </div>

            <!-- Reported Properties Tab -->
            <div id="reportedContent" class="tab-content p-6 hidden">
                <div id="reportedProperties" class="space-y-4">
                    <!-- Properties will be loaded here -->
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentTab = 'pending';

        // Initialize admin panel
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardStats();
            loadPendingProperties();
        });

        // Tab switching
        function showTab(tab) {
            currentTab = tab;
            
            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.className = 'tab-button py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300';
            });
            document.getElementById(tab + 'Tab').className = 'tab-button py-4 px-1 border-b-2 font-medium text-sm border-primary text-primary';
            
            // Update content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            document.getElementById(tab + 'Content').classList.remove('hidden');
            
            // Load appropriate data
            if (tab === 'pending') {
                loadPendingProperties();
            } else if (tab === 'reported') {
                loadReportedProperties();
            }
        }

        // Load dashboard statistics
        async function loadDashboardStats() {
            try {
                const response = await fetch('/admin', {
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123') // Replace with actual credentials
                    }
                });
                
                if (!response.ok) throw new Error('Failed to load stats');
                
                const data = await response.json();
                if (data.success) {
                    document.getElementById('pendingCount').textContent = data.stats.pending;
                    document.getElementById('reportedCount').textContent = data.stats.reported;
                    document.getElementById('approvedCount').textContent = data.stats.approved;
                    document.getElementById('totalCount').textContent = data.stats.total;
                }
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
            }
        }

        // Load pending properties
        async function loadPendingProperties() {
            try {
                const response = await fetch('/admin/properties/pending', {
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123') // Replace with actual credentials
                    }
                });
                
                if (!response.ok) throw new Error('Failed to load pending properties');
                
                const data = await response.json();
                if (data.success) {
                    renderPendingProperties(data.data);
                }
            } catch (error) {
                console.error('Error loading pending properties:', error);
            }
        }

        // Load reported properties
        async function loadReportedProperties() {
            try {
                const response = await fetch('/admin/properties/reported', {
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123') // Replace with actual credentials
                    }
                });
                
                if (!response.ok) throw new Error('Failed to load reported properties');
                
                const data = await response.json();
                if (data.success) {
                    renderReportedProperties(data.data);
                }
            } catch (error) {
                console.error('Error loading reported properties:', error);
            }
        }

        // Render pending properties
        function renderPendingProperties(properties) {
            const container = document.getElementById('pendingProperties');
            
            if (properties.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">No pending properties</p>';
                return;
            }
            
            container.innerHTML = properties.map(property => `
                <div class="border rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">${property.title}</h3>
                            <p class="text-gray-600">${property.city} - $${property.price}/month</p>
                            <p class="text-sm text-gray-500 mt-1">${property.bedrooms} bed, ${property.bathrooms} bath</p>
                            <p class="text-sm text-gray-500">Created: ${new Date(property.created_at).toLocaleDateString()}</p>
                        </div>
                        <div class="flex space-x-2 ml-4">
                            <button onclick="approveProperty('${property._id}')" 
                                    class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                                <i class="fas fa-check mr-1"></i>Approve
                            </button>
                            <button onclick="rejectProperty('${property._id}')" 
                                    class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                                <i class="fas fa-times mr-1"></i>Reject
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Render reported properties
        function renderReportedProperties(properties) {
            const container = document.getElementById('reportedProperties');
            
            if (properties.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">No reported properties</p>';
                return;
            }
            
            container.innerHTML = properties.map(property => `
                <div class="border rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">${property.title}</h3>
                            <p class="text-gray-600">${property.city} - $${property.price}/month</p>
                            <p class="text-sm text-red-600 font-medium">${property.report_count} report(s)</p>
                        </div>
                        <div class="flex space-x-2 ml-4">
                            <button onclick="dismissReports('${property._id}')" 
                                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                <i class="fas fa-check mr-1"></i>Dismiss
                            </button>
                            <button onclick="deleteProperty('${property._id}')" 
                                    class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                                <i class="fas fa-trash mr-1"></i>Delete
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Admin actions
        async function approveProperty(propertyId) {
            if (!confirm('Approve this property?')) return;
            
            try {
                const response = await fetch(`/admin/properties/${propertyId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123')
                    }
                });
                
                if (response.ok) {
                    loadDashboardStats();
                    loadPendingProperties();
                    alert('Property approved successfully');
                }
            } catch (error) {
                console.error('Error approving property:', error);
                alert('Failed to approve property');
            }
        }

        async function rejectProperty(propertyId) {
            if (!confirm('Reject this property?')) return;
            
            try {
                const response = await fetch(`/admin/properties/${propertyId}/reject`, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123')
                    }
                });
                
                if (response.ok) {
                    loadDashboardStats();
                    loadPendingProperties();
                    alert('Property rejected successfully');
                }
            } catch (error) {
                console.error('Error rejecting property:', error);
                alert('Failed to reject property');
            }
        }

        async function dismissReports(propertyId) {
            if (!confirm('Dismiss all reports for this property?')) return;
            
            try {
                const response = await fetch(`/admin/properties/${propertyId}/dismiss-reports`, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123')
                    }
                });
                
                if (response.ok) {
                    loadDashboardStats();
                    loadReportedProperties();
                    alert('Reports dismissed successfully');
                }
            } catch (error) {
                console.error('Error dismissing reports:', error);
                alert('Failed to dismiss reports');
            }
        }

        async function deleteProperty(propertyId) {
            if (!confirm('Delete this property permanently? This action cannot be undone.')) return;
            
            try {
                const response = await fetch(`/admin/properties/${propertyId}/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123')
                    }
                });
                
                if (response.ok) {
                    loadDashboardStats();
                    loadReportedProperties();
                    alert('Property deleted successfully');
                }
            } catch (error) {
                console.error('Error deleting property:', error);
                alert('Failed to delete property');
            }
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = '/';
            }
        }
    </script>
</body>
</html>
