#!/usr/bin/env python3
"""
Debug admin authentication issue
"""

import os
from dotenv import load_dotenv
import requests
import base64

def debug_env_variables():
    """Debug environment variables"""
    print("🔍 Debugging Environment Variables...")
    
    # Load environment variables
    load_dotenv()
    
    admin_user = os.getenv('ADMIN_USER')
    admin_pass = os.getenv('ADMIN_PASS')
    
    print(f"ADMIN_USER from env: '{admin_user}' (length: {len(admin_user) if admin_user else 0})")
    print(f"ADMIN_PASS from env: '{admin_pass}' (length: {len(admin_pass) if admin_pass else 0})")
    
    # Check for hidden characters
    if admin_user:
        print(f"ADMIN_USER bytes: {admin_user.encode()}")
    if admin_pass:
        print(f"ADMIN_PASS bytes: {admin_pass.encode()}")
    
    return admin_user, admin_pass

def test_direct_auth():
    """Test authentication directly"""
    print("\n🧪 Testing Direct Authentication...")
    
    admin_user, admin_pass = debug_env_variables()
    
    if not admin_user or not admin_pass:
        print("❌ Admin credentials not found in environment")
        return False
    
    # Test with exact credentials from .env
    try:
        response = requests.get(
            'http://localhost:5000/admin',
            auth=(admin_user, admin_pass),
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Authentication successful with .env credentials")
            return True
        elif response.status_code == 401:
            print("❌ Authentication failed with .env credentials")
            
            # Try with hardcoded values to compare
            print("\n🔍 Testing with hardcoded values...")
            response2 = requests.get(
                'http://localhost:5000/admin',
                auth=('admin', 'Interpreter@1435'),
                timeout=10
            )
            
            if response2.status_code == 200:
                print("✅ Hardcoded credentials work - issue is with .env loading")
            else:
                print("❌ Even hardcoded credentials fail - server issue")
            
            return False
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_login_form():
    """Test the login form authentication"""
    print("\n🧪 Testing Login Form Authentication...")
    
    admin_user, admin_pass = debug_env_variables()
    
    try:
        # Create basic auth header like the login form does
        credentials = base64.b64encode(f"{admin_user}:{admin_pass}".encode()).decode()
        
        response = requests.get(
            'http://localhost:5000/admin',
            headers={'Authorization': f'Basic {credentials}'},
            timeout=10
        )
        
        print(f"Login form auth response: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Login form authentication method works")
            return True
        else:
            print("❌ Login form authentication method fails")
            return False
            
    except Exception as e:
        print(f"❌ Login form test failed: {e}")
        return False

def check_server_auth_function():
    """Check what the server is actually comparing"""
    print("\n🔍 Checking Server Authentication Logic...")
    
    # Read the current .env file
    try:
        with open('.env', 'r') as f:
            env_content = f.read()
        
        print("Current .env file content:")
        for line in env_content.split('\n'):
            if 'ADMIN_' in line:
                print(f"  {line}")
        
        # Check for common issues
        if '\r' in env_content:
            print("⚠️  WARNING: .env file contains Windows line endings (\\r)")
        
        if env_content.endswith('\n'):
            print("✅ .env file ends with newline")
        else:
            print("⚠️  .env file doesn't end with newline")
            
    except Exception as e:
        print(f"❌ Could not read .env file: {e}")

def main():
    """Run all debugging tests"""
    print("🔍 Admin Authentication Debug")
    print("=" * 50)
    
    # Check environment variables
    debug_env_variables()
    
    # Check .env file format
    check_server_auth_function()
    
    # Test direct authentication
    direct_success = test_direct_auth()
    
    # Test login form method
    form_success = test_login_form()
    
    print("\n" + "=" * 50)
    print("📊 Debug Results:")
    print(f"   Direct Auth: {'✅ PASSED' if direct_success else '❌ FAILED'}")
    print(f"   Form Auth: {'✅ PASSED' if form_success else '❌ FAILED'}")
    
    if not direct_success and not form_success:
        print("\n💡 Possible Issues:")
        print("   1. .env file has wrong format or encoding")
        print("   2. Server not loading .env variables correctly")
        print("   3. Hidden characters in credentials")
        print("   4. Server authentication logic has bugs")
        
        print("\n🔧 Suggested Fixes:")
        print("   1. Recreate .env file with simple text editor")
        print("   2. Restart the server after .env changes")
        print("   3. Check for trailing spaces in credentials")
        print("   4. Use hardcoded values temporarily for testing")
    
    return direct_success or form_success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
